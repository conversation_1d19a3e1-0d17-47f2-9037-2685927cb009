package utils

import (
	"dh52110724-api-quan-ly-nha-thong-minh/config"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

type JWTManager struct {
	config *config.Config
}

var jwtManagerInstance *JWTManager

func GetJWTManager() *JWTManager {
	if jwtManagerInstance == nil {
		jwtManagerInstance = &JWTManager{
			config: config.LoadConfig(),
		}
	}
	return jwtManagerInstance
}


func (m *JWTManager) GenerateToken(email string, userID *int) (string, error) {
	claims := jwt.MapClaims{
		"email":   email,
		"user_id": userID,
		"exp":     time.Now().Add(time.Hour * 24).Unix(),
		"iat":     time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(m.config.JWTSecret))
}

func (m *JWTManager) ValidateToken(tokenString string) (*jwt.Token, error) {
	return jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(m.config.JWTSecret), nil
	})
}
