package models

import (
	"time"
)

type Device struct {
	DeviceID int    `json:"device_id" gorm:"primaryKey;column:device_id"`
	Name     string `json:"name" gorm:"column:name;type:varchar(100)"`
	DeviceTypeID     *int      `json:"device_type_id" gorm:"column:device_type_id"`
	HomeID           *int      `json:"home_id" gorm:"column:home_id"`
	AreaID           *int      `json:"area_id" gorm:"column:area_id"`
	ConnectionTypeID int       `json:"connection_type_id" gorm:"column:connection_type_id"`
	UniqueIdentifier string    `json:"unique_identifier" gorm:"column:unique_identifier;type:varchar(100);uniqueIndex"`
	IsOnline         bool      `json:"is_online" gorm:"column:is_online;type:tinyint(1)"`
	Status           string    `json:"status" gorm:"column:status;type:varchar(20);default:'active'"`
	CreatedAt        time.Time `json:"created_at" gorm:"column:created_at;type:timestamp"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"column:updated_at;type:timestamp"`

	DeviceType  *DeviceType        `json:"device_type,omitempty" gorm:"foreignKey:DeviceTypeID"`
	Area        *Area              `json:"area,omitempty" gorm:"foreignKey:AreaID"`
	Home        *Home              `json:"home,omitempty" gorm:"foreignKey:HomeID"`
	NetworkInfo *DeviceNetworkInfo `json:"network_info,omitempty" gorm:"foreignKey:DeviceID"`
}

func (Device) TableName() string {
	return "devices"
}

type DeviceType struct {
	DeviceTypeID int       `json:"device_type_id" gorm:"primaryKey;column:device_type_id"`
	Name         string    `json:"name" gorm:"column:name;type:varchar(50);uniqueIndex"`
	Description  string    `json:"description" gorm:"column:description;type:text"`
	IconURL      string    `json:"icon_url" gorm:"column:icon_url;type:varchar(255)"`
	IsActive     bool      `json:"is_active" gorm:"column:is_active;type:tinyint(1);default:1"`
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;type:timestamp"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"column:updated_at;type:timestamp"`
}

func (DeviceType) TableName() string {
	return "device_types"
}

type DeviceTypeDescription struct {
	DeviceTypeID int    `json:"device_type_id" gorm:"primaryKey;column:device_type_id"`
	Description  string `json:"description" gorm:"column:description;type:text"`
	IconURL      string `json:"icon_url" gorm:"column:icon_url;type:varchar(255)"`
}

func (DeviceTypeDescription) TableName() string {
	return "device_type_id_descriptions"
}

type ConnectionType struct {
	ConnectionTypeID int    `json:"connection_type_id" gorm:"primaryKey;column:connection_type_id"`
	Name             string `json:"name" gorm:"column:name;type:varchar(50);uniqueIndex"`
}

func (ConnectionType) TableName() string {
	return "connection_types"
}

type ConnectionTypeDescription struct {
	ConnectionTypeID int    `json:"connection_type_id" gorm:"primaryKey;column:connection_type_id"`
	Description      string `json:"description" gorm:"column:description;type:text"`
}

func (ConnectionTypeDescription) TableName() string {
	return "connection_type_descriptions"
}

type DeviceState struct {
	DeviceID   int       `json:"device_id" gorm:"primaryKey;column:device_id"`
	PropertyID int       `json:"property_id" gorm:"primaryKey;column:property_id"`
	Value      string    `json:"value" gorm:"column:value;type:text"`
	Timestamp  time.Time `json:"timestamp" gorm:"column:timestamp;default:CURRENT_TIMESTAMP"`
}

func (DeviceState) TableName() string {
	return "device_states"
}

type DeviceProperty struct {
	PropertyID int  `json:"property_id" gorm:"primaryKey;column:property_id"`
	DeviceID   int  `json:"device_id" gorm:"column:device_id"`
	TemplateID int  `json:"template_id" gorm:"column:template_id"`
	Readable   bool `json:"readable" gorm:"column:readable;default:1"`
	Writable   bool `json:"writable" gorm:"column:writable;default:1"`
}

func (DeviceProperty) TableName() string {
	return "device_properties"
}

type PropertyTemplate struct {
	TemplateID   int    `json:"template_id" gorm:"primaryKey;column:template_id"`
	PropertyName string `json:"property_name" gorm:"column:property_name;type:varchar(50)"`
	PropertyType string `json:"property_type" gorm:"column:property_type;type:enum('boolean','integer','float','string')"`
}

func (PropertyTemplate) TableName() string {
	return "property_templates"
}

type UpdateDeviceRequest struct {
	Name string `json:"name"`
	AreaID *int   `json:"area_id"`
	Status string `json:"status"`
}

type DeviceNetworkInfo struct {
	DeviceID      int        `json:"device_id" gorm:"primaryKey;column:device_id"`
	IPAddress     string     `json:"ip_address" gorm:"column:ip_address;type:varchar(45)"`
	MACAddress    string     `json:"mac_address" gorm:"column:mac_address;type:varchar(17)"`
	SubnetMask    string     `json:"subnet_mask" gorm:"column:subnet_mask;type:varchar(15)"`
	Gateway       string     `json:"gateway" gorm:"column:gateway;type:varchar(45)"`
	DNSServer     string     `json:"dns_server" gorm:"column:dns_server;type:varchar(45)"`
	LastConnected *time.Time `json:"last_connected" gorm:"column:last_connected"`
}

func (DeviceNetworkInfo) TableName() string {
	return "device_network_info"
}

type DeviceCommand struct {
	CommandID  int        `gorm:"primaryKey;column:command_id;autoIncrement"`
	DeviceID   int        `gorm:"column:device_id;not null"`
	UserID     int        `gorm:"column:user_id;not null"`
	Command    string     `gorm:"column:command;type:varchar(100);not null"`
	Value      string     `gorm:"column:value;type:text"`
	Status     string     `gorm:"column:status;type:varchar(20);default:'pending'"`
	CreatedAt  time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	ExecutedAt *time.Time `gorm:"column:executed_at"`
	Device     Device     `gorm:"foreignKey:DeviceID"`
}

func (DeviceCommand) TableName() string {
	return "device_commands"
}

type DeviceCommandRequest struct {
	Command    string `json:"command" binding:"required"`
	Parameters string `json:"parameters"`
}

type DeviceDiscoveryRequest struct {
	ConnectionTypeID int    `json:"connection_type_id" binding:"required"`
	NetworkAddress   string `json:"network_address,omitempty"`
	Timeout          int    `json:"timeout,omitempty"`
}

type DiscoveredDevice struct {
	Name             string             `json:"name"`
	DeviceTypeID     int                `json:"device_type_id"`
	ConnectionTypeID int                `json:"connection_type_id"`
	UniqueIdentifier string             `json:"unique_identifier"`
	Manufacturer     string             `json:"manufacturer,omitempty"`
	Model            string             `json:"model,omitempty"`
	NetworkInfo      *DeviceNetworkInfo `json:"network_info,omitempty"`
}

type DeviceConnectionStatus struct {
	DeviceID       int    `json:"device_id"`
	Name           string `json:"name"`
	IsOnline       bool   `json:"is_online"`
	LastSeen       string `json:"last_seen,omitempty"`
	SignalStrength int    `json:"signal_strength,omitempty"`
}





type DeviceCapability struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Readable    bool        `json:"readable"`
	Writable    bool        `json:"writable"`
	MinValue    interface{} `json:"min_value,omitempty"`
	MaxValue    interface{} `json:"max_value,omitempty"`
	EnumValues  []string    `json:"enum_values,omitempty"`
	Unit        string      `json:"unit,omitempty"`
	Description string      `json:"description,omitempty"`
}

type DeviceControlCommand struct {
	Command     string                 `json:"command"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

type DiscoveryRequest struct {
	ConnectionTypes []int `json:"connection_types" binding:"required"`
	ScanDuration    int   `json:"scan_duration,omitempty"`
}

type DiscoveryResponse struct {
	Status          string             `json:"status"`
	Message         string             `json:"message"`
	DevicesFound    []DiscoveredDevice `json:"devices_found"`
	ScanDuration    int                `json:"scan_duration"`
	ConnectionTypes []string           `json:"connection_types"`
	HomeID          int                `json:"home_id" gorm:"column:home_id"`
}

type AddDeviceToHomeRequest struct {
	Name             string             `json:"name" binding:"required"`
	DeviceTypeID     *int               `json:"device_type_id"`
	ConnectionTypeID int                `json:"connection_type_id" binding:"required"`
	UniqueIdentifier string             `json:"unique_identifier" binding:"required"`
	AreaID           *int               `json:"area_id,omitempty"`
	NetworkInfo      *DeviceNetworkInfo `json:"network_info,omitempty"`
}

type DeleteDeviceRequest struct {
	DeviceID int `json:"device_id" binding:"required"`
}

type RealTimeDeviceStatus struct {
	DeviceID    int       `json:"device_id"`
	Name        string    `json:"name"`
	DeviceType  string    `json:"device_type_id"`
	AreaName    string    `json:"area_name"`
	IsOnline    bool      `json:"is_online"`
	Value       string    `json:"value"`
	LastUpdated time.Time `json:"last_updated"`
}

type BatchControlRequest struct {
	DeviceCommands []struct {
		DeviceID int    `json:"device_id" binding:"required"`
		Command  string `json:"command" binding:"required"`
		Value    string `json:"value,omitempty"`
	} `json:"device_commands" binding:"required,dive"`
	ExecuteSequentially bool `json:"execute_sequentially"`
}

type BatchControlResponse struct {
	Status  string               `json:"status"`
	Message string               `json:"message"`
	Results []BatchControlResult `json:"results"`
}

type BatchControlResult struct {
	DeviceID int    `json:"device_id"`
	Success  bool   `json:"success"`
	Message  string `json:"message,omitempty"`
	Error    string `json:"error,omitempty"`
}
