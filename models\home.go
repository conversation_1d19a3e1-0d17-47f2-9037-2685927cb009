package models

import "time"

type Home struct {
	HomeID    int       `json:"home_id" gorm:"primaryKey;column:home_id"`
	Name      string    `json:"name" gorm:"column:name;type:varchar(100)"`
	Address   string    `json:"address" gorm:"column:address;type:text"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP;<-:create"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
	Areas     []Area    `json:"areas,omitempty" gorm:"foreignKey:HomeID"`
	Devices   []Device  `json:"devices,omitempty" gorm:"foreignKey:HomeID"`
}

func (Home) TableName() string {
	return "homes"
}

type HomeRequest struct {
	Name    string `json:"name" binding:"required"`
	Address string `json:"address" binding:"required"`
}

type HomeUserRequest struct {
	Email  string `json:"email" binding:"required,email"`
	RoleID int    `json:"role_id,omitempty"`
}

type PromoteUserRequest struct {
	UserID int `json:"user_id" binding:"required"`
	RoleID int `json:"role_id" binding:"required,min=1,max=3"`
}

type TransferOwnershipRequest struct {
	NewOwnerID int `json:"new_owner_id" binding:"required"`
}

type HomeInvitationRequest struct {
	Email  string `json:"email" binding:"required,email"`
	RoleID int    `json:"role_id,omitempty"`
}

type AcceptInvitationRequest struct {
	Token string `json:"token" binding:"required"`
}

type HomeInvitationResponse struct {
	InvitationID int        `json:"invitation_id"`
	HomeID       int        `json:"home_id"`
	InviterID    int        `json:"inviter_id"`
	Email        string     `json:"email"`
	RoleID       int        `json:"role_id"`
	Token        string     `json:"token"`
	Status       string     `json:"status"`
	ExpiresAt    time.Time  `json:"expires_at"`
	CreatedAt    time.Time  `json:"created_at"`
	AcceptedAt   *time.Time `json:"accepted_at"`

	InviterName string `json:"inviter_name"`
	HomeName    string `json:"home_name"`
	RoleName    string `json:"role_name"`
}

type HomeUserResponse struct {
	UserID    int    `json:"user_id"`
	Email     string `json:"email"`
	FullName  string `json:"full_name"`
	RoleID    int    `json:"role_id"`
	RoleName  string `json:"role_name"`
	CreatedAt string `json:"created_at"`
}

type HomeStatistics struct {
	TotalAreas       int                 `json:"total_areas"`
	TotalDevices     int                 `json:"total_devices"`
	OnlineDevices    int                 `json:"online_devices"`
	OfflineDevices   int                 `json:"offline_devices"`
	TotalUsers       int                 `json:"total_users"`
	DevicesByType    []DeviceTypeCount   `json:"devices_by_type"`
	DevicesByArea    []AreaDeviceCount   `json:"devices_by_area"`
	RecentActivities []RecentActivity    `json:"recent_activities"`
	EnergyUsage      *EnergyUsageSummary `json:"energy_usage,omitempty"`
}

type DeviceTypeCount struct {
	DeviceTypeID   int    `json:"device_type_id"`
	DeviceTypeName string `json:"device_type_name"`
	Count          int    `json:"count"`
	OnlineCount    int    `json:"online_count"`
}

type AreaDeviceCount struct {
	AreaID      int    `json:"area_id"`
	AreaName    string `json:"area_name"`
	TotalCount  int    `json:"total_count"`
	OnlineCount int    `json:"online_count"`
}

type RecentActivity struct {
	ActivityID  int    `json:"activity_id"`
	DeviceID    int    `json:"device_id"`
	DeviceName  string `json:"device_name"`
	AreaName    string `json:"area_name"`
	Action      string `json:"action"`
	Description string `json:"description"`
	Timestamp   string `json:"timestamp"`
	UserID      *int   `json:"user_id,omitempty"`
	UserName    string `json:"user_name,omitempty"`
}

type EnergyUsageSummary struct {
	TotalUsageToday     float64       `json:"total_usage_today"`
	TotalUsageThisWeek  float64       `json:"total_usage_this_week"`
	TotalUsageThisMonth float64       `json:"total_usage_this_month"`
	UsageByDay          []DailyUsage  `json:"usage_by_day"`
	TopConsumingDevices []DeviceUsage `json:"top_consuming_devices"`
}

type DailyUsage struct {
	Date  string  `json:"date"`
	Usage float64 `json:"usage"`
}

type DeviceUsage struct {
	DeviceID   int     `json:"device_id"`
	DeviceName string  `json:"device_name"`
	AreaName   string  `json:"area_name"`
	Usage      float64 `json:"usage"`
}

type HomeDetailResponse struct {
	*Home        `json:",inline"`
	Statistics   *HomeStatistics `json:"statistics"`
	UserRole     int             `json:"user_role"`
	UserRoleName string          `json:"user_role_name"`
}

type Area struct {
	AreaID    int       `json:"area_id" gorm:"primaryKey;column:area_id"`
	HomeID    int       `json:"home_id" gorm:"column:home_id"`
	Name      string    `json:"name" gorm:"column:name;type:varchar(50)"`
	Devices   []Device  `json:"devices,omitempty" gorm:"foreignKey:AreaID;references:AreaID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP;<-:create"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
}

func (Area) TableName() string {
	return "areas"
}

type AreaDescription struct {
	AreaID      int    `json:"area_id" gorm:"primaryKey;column:area_id"`
	Description string `json:"description" gorm:"column:description;type:text"`
}

func (AreaDescription) TableName() string {
	return "area_descriptions"
}

type HomeUser struct {
	HomeID    int       `json:"home_id" gorm:"primaryKey;column:home_id"`
	UserID    int       `json:"user_id" gorm:"primaryKey;column:user_id"`
	RoleID    int       `json:"role_id" gorm:"column:role_id;default:3"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`

	Home *Home `json:"home,omitempty" gorm:"foreignKey:HomeID"`
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Role *Role `json:"role,omitempty" gorm:"foreignKey:RoleID"`
}

func (HomeUser) TableName() string {
	return "home_users"
}

type HomeInvitation struct {
	InvitationID int        `json:"invitation_id" gorm:"primaryKey;column:invitation_id;autoIncrement"`
	HomeID       int        `json:"home_id" gorm:"column:home_id;not null"`
	InviterID    int        `json:"inviter_id" gorm:"column:inviter_id;not null"`
	Email        string     `json:"email" gorm:"column:email;type:varchar(255);not null"`
	RoleID       int        `json:"role_id" gorm:"column:role_id;default:3"`
	Token        string     `json:"token" gorm:"column:token;type:varchar(255);uniqueIndex;not null"`
	Status       string     `json:"status" gorm:"column:status;type:enum('pending','accepted','rejected','expired');default:'pending'"`
	ExpiresAt    time.Time  `json:"expires_at" gorm:"column:expires_at;not null"`
	CreatedAt    time.Time  `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	AcceptedAt   *time.Time `json:"accepted_at" gorm:"column:accepted_at"`

	Home    *Home `json:"home,omitempty" gorm:"foreignKey:HomeID"`
	Inviter *User `json:"inviter,omitempty" gorm:"foreignKey:InviterID"`
	Role    *Role `json:"role,omitempty" gorm:"foreignKey:RoleID"`
}

func (HomeInvitation) TableName() string {
	return "home_invitations"
}
