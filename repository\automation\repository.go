package automation

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"time"
)

type Repository interface {
	CreateRule(rule *models.AutomationRule) (int, error)
	GetRuleByID(ruleID int) (*models.AutomationRule, error)
	GetRulesByUserID(userID int) ([]models.AutomationRule, error)
	GetRulesByHomeID(homeID int) ([]models.AutomationRule, error)
	UpdateRule(rule *models.AutomationRule) error
	UpdateRuleStatus(ruleID int, isActive bool) error
	DeleteRule(ruleID int) error
	GetActiveConditionRules() ([]models.AutomationRule, error)
	GetActiveScheduleRules() ([]models.AutomationRule, error)

	CreateCondition(condition *models.RuleCondition) error
	GetConditionsByRuleID(ruleID int) ([]models.RuleCondition, error)
	UpdateCondition(condition *models.RuleCondition) error
	DeleteConditionsByRuleID(ruleID int) error

	CreateAction(action *models.RuleAction) error
	GetActionsByRuleID(ruleID int) ([]models.RuleAction, error)
	UpdateAction(action *models.RuleAction) error
	DeleteActionsByRuleID(ruleID int) error

	CreateSchedule(schedule *models.RuleSchedule) error
	GetScheduleByRuleID(ruleID int) (*models.RuleSchedule, error)
	UpdateSchedule(schedule *models.RuleSchedule) error
	DeleteScheduleByRuleID(ruleID int) error
	GetSchedulesDueForExecution() ([]models.RuleSchedule, error)
	UpdateScheduleNextRun(ruleID int, nextRun *time.Time) error

	GetRuleTemplates() ([]models.RuleTemplate, error)
	GetRuleTemplateByID(templateID int) (*models.RuleTemplate, error)
	CreateRuleTemplate(template *models.RuleTemplate) (int, error)

	CreateExecutionLog(log *models.RuleExecutionLog) error
	GetExecutionHistory(ruleID int, limit int) ([]models.RuleExecutionLog, error)
	GetExecutionHistoryByUserID(userID int, limit int) ([]models.RuleExecutionLog, error)
	DeleteOldExecutionLogs(days int) error
}
