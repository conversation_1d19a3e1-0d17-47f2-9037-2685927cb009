package utils

import (
	"crypto/rand"
	"dh52110724-api-quan-ly-nha-thong-minh/config"
	"errors"
	"fmt"
	"log"
	"math/big"
	"net/smtp"
	"strconv"
	"sync"
	"time"
)

var (
	cfg        *config.Config
	jwtManager *JWTManager
)

func init() {
	cfg = config.LoadConfig()
	jwtManager = GetJWTManager()
}

type OTPData struct {
	Code          string
	CreatedAt     time.Time
	ExpiresAt     time.Time
	IsUsed        bool
	AttemptsCount int
}

type OTPStore struct {
	otps  map[string]*OTPData
	mutex sync.RWMutex
}

var (
	otpStore     *OTPStore
	otpStoreOnce sync.Once
	otpLength    = 6
	otpExpiresIn = 10 * time.Minute
)
var (
	ErrOTPNotFound         = errors.New("không tìm thấy OTP")
	ErrOTPExpired          = errors.New("OTP đã hết hạn")
	ErrOTPUsed             = errors.New("OTP đã được sử dụng")
	ErrOTPInvalid          = errors.New("mã OTP không hợp lệ")
	ErrMaxAttemptsExceeded = errors.New("đã vượt quá số lần thử tối đa")
	ErrTokenGeneration     = errors.New("không thể tạo JWT token")
)

const (
	OTPLength       = 6
	OTPExpiry       = 10 * time.Minute
	MaxOTPAttempts  = 5
	CleanupInterval = 5 * time.Minute
)

func GetOTPStore() *OTPStore {
	otpStoreOnce.Do(func() {
		otpStore = &OTPStore{
			otps: make(map[string]*OTPData),
		}
		go otpStore.startCleanupRoutine()
	})
	return otpStore
}

func (s *OTPStore) startCleanupRoutine() {
	ticker := time.NewTicker(CleanupInterval)
	defer ticker.Stop()

	log.Printf("OTP cleanup routine started - interval: %v", CleanupInterval)

	for range ticker.C {
		s.CleanupExpiredOTPs()
	}
}


func (s *OTPStore) GenerateOTP(email string) string {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	log.Printf("=== OTP Generation Start ===")
	log.Printf("Generating OTP for email: %s", email)
	log.Printf("Current store size: %d", len(s.otps))

	if _, exists := s.otps[email]; exists {
		log.Printf("Removing existing OTP for email: %s", email)
		delete(s.otps, email)
	}

	var code string
	maxRetries := 10
	for i := 0; i < maxRetries; i++ {
		code = generateRandomOTP()

		isUnique := true
		for otherEmail, otpData := range s.otps {
			if otpData.Code == code && !otpData.IsUsed && time.Now().Before(otpData.ExpiresAt) {
				log.Printf("OTP collision detected with email: %s, retrying...", otherEmail)
				isUnique = false
				break
			}
		}

		if isUnique {
			break
		}
	}

	now := time.Now()
	s.otps[email] = &OTPData{
		Code:          code,
		CreatedAt:     now,
		ExpiresAt:     now.Add(otpExpiresIn),
		IsUsed:        false,
		AttemptsCount: 0,
	}

	log.Printf("Generated new OTP for email: %s", email)
	log.Printf("OTP Code: %s", code)
	log.Printf("OTP Expires at: %v", now.Add(otpExpiresIn))
	log.Printf("Current store size after generation: %d", len(s.otps))

	return code
}

func (s *OTPStore) VerifyOTP(email, code string, userID *string) (bool, string, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	log.Printf("=== DEBUG VERIFY OTP DETAILED ===")
	log.Printf("Email: %s", email)
	log.Printf("Code: %s", code)
	log.Printf("Store size: %d", len(s.otps))

	otp, exists := s.otps[email]
	if !exists {
		log.Printf("OTP not found for email: %s", email)
		return false, "", ErrOTPNotFound
	}

	log.Printf("Found OTP - Code: %s, IsUsed: %v, ExpiresAt: %v, AttemptsCount: %d",
		otp.Code, otp.IsUsed, otp.ExpiresAt, otp.AttemptsCount)

	if otp.IsUsed {
		log.Printf("OTP already used for email: %s", email)
		delete(s.otps, email)
		return false, "", ErrOTPUsed
	}

	now := time.Now()
	if now.After(otp.ExpiresAt) {
		log.Printf("OTP expired for email: %s. Now: %v, ExpiresAt: %v", email, now, otp.ExpiresAt)
		delete(s.otps, email)
		return false, "", ErrOTPExpired
	}

	if otp.Code != code {
		log.Printf("OTP code mismatch for email: %s. Expected: %s, Got: %s", email, otp.Code, code)
		otp.AttemptsCount++
		if otp.AttemptsCount >= MaxOTPAttempts {
			log.Printf("Max attempts exceeded for email: %s", email)
			delete(s.otps, email)
			return false, "", ErrMaxAttemptsExceeded
		}
		s.otps[email] = otp
		return false, "", ErrOTPInvalid
	}
	var userIDInt *int
	if userID != nil {
		if id, err := strconv.Atoi(*userID); err == nil {
			userIDInt = &id
		}
	}

	if jwtManager == nil {
		jwtManager = GetJWTManager()
	}

	token, err := jwtManager.GenerateToken(email, userIDInt)
	if err != nil {
		return false, "", err
	}

	otp.IsUsed = true
	s.otps[email] = otp

	return true, token, nil
}

func (s *OTPStore) ClearOTP(email string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if _, exists := s.otps[email]; exists {
		log.Printf("Clearing existing OTP for email: %s", email)
		delete(s.otps, email)
	}
}

func (s *OTPStore) CleanupExpiredOTPs() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	log.Printf("=== OTP Cleanup Start ===")
	log.Printf("Current store size before cleanup: %d", len(s.otps))

	now := time.Now()
	count := 0
	var cleanedEmails []string

	for email, otp := range s.otps {
		isExpired := now.After(otp.ExpiresAt)
		isUsed := otp.IsUsed

		if isExpired || isUsed {
			timeExpired := ""
			if isExpired {
				timeExpired = fmt.Sprintf(" (expired %v ago)", now.Sub(otp.ExpiresAt))
			}

			log.Printf("Cleaning up OTP for %s - Expired: %v%s, Used: %v",
				email, isExpired, timeExpired, isUsed)
			delete(s.otps, email)
			cleanedEmails = append(cleanedEmails, email)
			count++
		}
	}

	if count > 0 {
		log.Printf("Cleaned up %d expired/used OTPs: %v", count, cleanedEmails)
	} else {
		log.Printf("No OTPs needed cleanup")
	}

	log.Printf("Current store size after cleanup: %d", len(s.otps))
	log.Printf("=== OTP Cleanup End ===")
}

func (s *OTPStore) GetOTPInfo(email string) (*OTPData, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	otp, exists := s.otps[email]
	if !exists {
		return nil, false
	}

	return &OTPData{
		Code:          otp.Code,
		CreatedAt:     otp.CreatedAt,
		ExpiresAt:     otp.ExpiresAt,
		IsUsed:        otp.IsUsed,
		AttemptsCount: otp.AttemptsCount,
	}, true
}

func (s *OTPStore) GetStoreSize() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return len(s.otps)
}

func generateRandomOTP() string {
	max := big.NewInt(1000000)
	n, err := rand.Int(rand.Reader, max)
	if err != nil {
		log.Printf("Error generating random OTP: %v", err)
		fallbackOTP := fmt.Sprintf("%06d", time.Now().UnixNano()%1000000)
		log.Printf("Using fallback OTP: %s", fallbackOTP)
		return fallbackOTP
	}

	otp := fmt.Sprintf("%06d", n.Int64())
	log.Printf("Generated OTP: %s", otp)
	return otp
}

func SendOTPEmail(to, otp string) error {
	log.Printf("=== Sending OTP Email ===")
	log.Printf("To: %s", to)
	log.Printf("OTP: %s", otp)

	cfg := config.LoadConfig()

	auth := smtp.PlainAuth("", cfg.SMTPUsername, cfg.SMTPPassword, cfg.SMTPHost)

	subject := "Mã xác thực OTP - Smart Home System"
	body := fmt.Sprintf(`
Xin chào,

Đây là mã xác thực OTP của bạn: %s

Mã này sẽ hết hạn sau %d phút.
Vui lòng không chia sẻ mã này với bất kỳ ai.

Trân trọng,
Smart Home System
	`, otp, int(OTPExpiry.Minutes()))

	msg := fmt.Sprintf("From: %s\r\n"+
		"To: %s\r\n"+
		"Subject: %s\r\n"+
		"Content-Type: text/plain; charset=UTF-8\r\n"+
		"\r\n"+
		"%s", cfg.SMTPFrom, to, subject, body)

	log.Printf("Sending email via SMTP: %s:%s", cfg.SMTPHost, cfg.SMTPPort)
	err := smtp.SendMail(
		fmt.Sprintf("%s:%s", cfg.SMTPHost, cfg.SMTPPort),
		auth,
		cfg.SMTPFrom,
		[]string{to},
		[]byte(msg),
	)

	if err != nil {
		log.Printf("Error sending OTP email: %v", err)
		return fmt.Errorf("không thể gửi email OTP: %v", err)
	}

	log.Printf("OTP email sent successfully to %s", to)
	return nil
}

func CleanupExpiredOTPs() {
	store := GetOTPStore()
	store.CleanupExpiredOTPs()
}

func SendAccountVerificationEmail(to, fullName, otp string) error {
	log.Printf("=== Sending Account Verification Email ===")
	log.Printf("To: %s", to)
	log.Printf("FullName: %s", fullName)
	log.Printf("OTP: %s", otp)

	cfg := config.LoadConfig()

	auth := smtp.PlainAuth("", cfg.SMTPUsername, cfg.SMTPPassword, cfg.SMTPHost)

	subject := "Xác thực tài khoản - Smart Home System"
	body := fmt.Sprintf(`
Xin chào %s,

Cảm ơn bạn đã đăng ký tài khoản Smart Home System.

Mã xác thực tài khoản của bạn: %s

Mã này sẽ hết hạn sau %d phút.
Vui lòng nhập mã này để hoàn tất đăng ký.

Trân trọng,
Smart Home System
	`, fullName, otp, int(OTPExpiry.Minutes()))

	msg := fmt.Sprintf("From: %s\r\n"+
		"To: %s\r\n"+
		"Subject: %s\r\n"+
		"Content-Type: text/plain; charset=UTF-8\r\n"+
		"\r\n"+
		"%s", cfg.SMTPFrom, to, subject, body)

	log.Printf("Sending account verification email via SMTP: %s:%s", cfg.SMTPHost, cfg.SMTPPort)
	err := smtp.SendMail(
		fmt.Sprintf("%s:%s", cfg.SMTPHost, cfg.SMTPPort),
		auth,
		cfg.SMTPFrom,
		[]string{to},
		[]byte(msg),
	)

	if err != nil {
		log.Printf("Error sending account verification email: %v", err)
		return fmt.Errorf("không thể gửi email xác thực: %v", err)
	}

	log.Printf("Account verification email sent successfully to %s", to)
	return nil
}

func SendInvitationEmail(to, inviterName, homeName, acceptURL string) error {
	log.Printf("=== Sending Invitation Email ===")
	log.Printf("To: %s", to)
	log.Printf("Inviter: %s", inviterName)
	log.Printf("Home: %s", homeName)
	log.Printf("Accept URL: %s", acceptURL)

	cfg := config.LoadConfig()

	auth := smtp.PlainAuth("", cfg.SMTPUsername, cfg.SMTPPassword, cfg.SMTPHost)

	subject := "Lời mời tham gia nhà thông minh - Smart Home System"
	body := fmt.Sprintf(`
Xin chào,

Bạn đã được %s mời tham gia vào nhà thông minh "%s".

Để chấp nhận lời mời này, vui lòng click vào link bên dưới:
%s

Lời mời này sẽ hết hạn sau 24 giờ.
Nếu bạn không muốn tham gia, bạn có thể bỏ qua email này.

Trân trọng,
Smart Home System
	`, inviterName, homeName, acceptURL)

	msg := fmt.Sprintf("From: %s\r\n"+
		"To: %s\r\n"+
		"Subject: %s\r\n"+
		"Content-Type: text/plain; charset=UTF-8\r\n"+
		"\r\n"+
		"%s", cfg.SMTPFrom, to, subject, body)

	log.Printf("Sending invitation email via SMTP: %s:%s", cfg.SMTPHost, cfg.SMTPPort)
	err := smtp.SendMail(
		fmt.Sprintf("%s:%s", cfg.SMTPHost, cfg.SMTPPort),
		auth,
		cfg.SMTPFrom,
		[]string{to},
		[]byte(msg),
	)

	if err != nil {
		log.Printf("Error sending invitation email: %v", err)
		return fmt.Errorf("không thể gửi email mời: %v", err)
	}

	log.Printf("Invitation email sent successfully to %s", to)
	return nil
}

func GenerateInvitationToken() (string, error) {
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", fmt.Errorf("không thể tạo token: %v", err)
	}

	return fmt.Sprintf("%x", bytes), nil
}
