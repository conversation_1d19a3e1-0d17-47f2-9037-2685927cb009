package controller

import (
	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type InvitationController struct {
	homeService *service.HomeService
}

func NewInvitationController(homeService *service.HomeService) *InvitationController {
	return &InvitationController{
		homeService: homeService,
	}
}

func (c *InvitationController) AcceptInvitation(ctx *gin.Context) {
	token := ctx.Param("token")
	if token == "" {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Token không hợp lệ")
		return
	}

	if err := c.homeService.AcceptInvitation(token); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Chấp nhận lời mời thành công! Bạn đã được thêm vào nhà.", nil)
}

func (c *InvitationController) AcceptInvitationPage(ctx *gin.Context) {
	token := ctx.Param("token")
	if token == "" {
		ctx.HTML(http.StatusBadRequest, "error.html", gin.H{
			"title":   "Lỗi",
			"message": "Token không hợp lệ",
		})
		return
	}

	ctx.HTML(http.StatusOK, "accept_invitation.html", gin.H{
		"title": "Chấp nhận lời mời",
		"token": token,
	})
}
