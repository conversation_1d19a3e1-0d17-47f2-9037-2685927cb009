package middleware

import (
	"dh52110724-api-quan-ly-nha-thong-minh/config"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

type AuthMiddleware struct {
	config *config.Config
}

func NewAuthMiddleware(cfg *config.Config) *AuthMiddleware {
	return &AuthMiddleware{
		config: cfg,
	}

}

func (am *AuthMiddleware) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {

		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			am.respondUnauthorized(c, "Không tìm thấy token xác thực")
			return
		}

		tokenString, err := am.extractBearerToken(authHeader)
		if err != nil {
			am.respondUnauthorized(c, err.Error())
			return
		}

		token, err := am.parseToken(tokenString)
		if err != nil {
			am.respondUnauthorized(c, "Token không hợp lệ hoặc đã hết hạn")
			return
		}

		claims, err := am.extractClaims(token)
		if err != nil {
			am.respondUnauthorized(c, "Không thể đọc thông tin từ token")
			return
		}

		if err := am.validateTokenExpiry(claims); err != nil {
			am.respondUnauthorized(c, "Token đã hết hạn")
			return
		}

		am.setUserContext(c, claims)

		c.Next()
	}
}

func (am *AuthMiddleware) extractBearerToken(authHeader string) (string, error) {
	const bearerPrefix = "Bearer "

	if !strings.HasPrefix(authHeader, bearerPrefix) {
		return "", fmt.Errorf("token không đúng định dạng Bearer")
	}

	tokenString := strings.TrimPrefix(authHeader, bearerPrefix)
	if strings.TrimSpace(tokenString) == "" {
		return "", fmt.Errorf("token rỗng")
	}

	return tokenString, nil
}

func (am *AuthMiddleware) parseToken(tokenString string) (*jwt.Token, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("signing method không hợp lệ: %v", token.Header["alg"])
		}
		return []byte(am.config.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("token không hợp lệ")
	}

	return token, nil
}

func (am *AuthMiddleware) extractClaims(token *jwt.Token) (jwt.MapClaims, error) {
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("không thể convert claims")
	}
	return claims, nil
}

func (am *AuthMiddleware) validateTokenExpiry(claims jwt.MapClaims) error {
	if exp, ok := claims["exp"]; ok {
		if expTime, ok := exp.(float64); ok {
			if time.Now().Unix() > int64(expTime) {
				return fmt.Errorf("token đã hết hạn")
			}
		}
	}
	return nil
}

func (am *AuthMiddleware) setUserContext(c *gin.Context, claims jwt.MapClaims) {
	log.Printf("=== Setting User Context ===")

	if email, ok := claims["email"].(string); ok {
		c.Set("email", email)
		log.Printf("Set email: %s", email)
	}

	if userID, exists := claims["user_id"]; exists {
		log.Printf("Found user_id in claims, type: %T, value: %v", userID, userID)

		switch v := userID.(type) {
		case float64:
			c.Set("user_id", uint(v))
			log.Printf("Set user_id (from float64): %d", uint(v))
		case int:
			c.Set("user_id", uint(v))
			log.Printf("Set user_id (from int): %d", uint(v))
		case string:
			if id, err := strconv.ParseUint(v, 10, 64); err == nil {
				c.Set("user_id", uint(id))
				log.Printf("Set user_id (from string): %d", uint(id))
			} else {
				log.Printf("Failed to parse user_id string: %v", err)
			}
		default:
			log.Printf("Warning: user_id is of unexpected type: %T", userID)
		}
	} else {
		log.Printf("No user_id found in claims")
	}

	log.Printf("=== Final Context State ===")
	log.Printf("Email: %v", c.GetString("email"))
	log.Printf("UserID: %v", c.GetUint("user_id"))
}

func GetEmail(c *gin.Context) (string, bool) {
	email, exists := c.Get("email")
	if !exists {
		return "", false
	}

	emailStr, ok := email.(string)
	if !ok {
		return "", false
	}

	return emailStr, true
}

func (am *AuthMiddleware) respondUnauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, gin.H{
		"status":  false,
		"message": message,
		"code":    "UNAUTHORIZED",
	})
	c.Abort()
}

func GetUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Printf("user_id not found in context")
		return 0, false
	}

	switch v := userID.(type) {
	case uint:
		return v, true
	case int:
		return uint(v), true
	case float64:
		return uint(v), true
	default:
		log.Printf("user_id is of unexpected type: %T", userID)
		return 0, false
	}
}

func GetUsername(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}

	if uname, ok := username.(string); ok {
		return uname, true
	}

	return "", false
}

func (am *AuthMiddleware) WebSocketAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenString := c.Query("token")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Không tìm thấy token xác thực"})
			c.Abort()
			return
		}

		token, err := am.parseToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token không hợp lệ hoặc đã hết hạn"})
			c.Abort()
			return
		}

		claims, err := am.extractClaims(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Không thể đọc thông tin từ token"})
			c.Abort()
			return
		}

		if err := am.validateTokenExpiry(claims); err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token đã hết hạn"})
			c.Abort()
			return
		}

		am.setUserContext(c, claims)

		c.Next()
	}
}

func (am *AuthMiddleware) RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			am.respondUnauthorized(c, "Không tìm thấy thông tin quyền")
			return
		}

		role, ok := userRole.(string)
		if !ok {
			am.respondUnauthorized(c, "Thông tin quyền không hợp lệ")
			return
		}

		for _, allowedRole := range roles {
			if role == allowedRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"status":  false,
			"message": "Không có quyền truy cập",
			"code":    "FORBIDDEN",
		})
		c.Abort()
	}
}
