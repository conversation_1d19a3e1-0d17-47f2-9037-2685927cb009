package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/automation"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/device"
	"fmt"
	"log"
	"strconv"
	"time"
)

type SensorEventHandler struct {
	automationRepo    automation.Repository
	deviceRepo        device.Repository
	automationService *AutomationService
	eventChannel      chan SensorEvent
	stopChannel       chan bool
	isRunning         bool
}

type SensorEvent struct {
	DeviceID   int       `json:"device_id"`
	PropertyID int       `json:"property_id"`
	Value      string    `json:"value"`
	Timestamp  time.Time `json:"timestamp"`
}

func NewSensorEventHandler(automationRepo automation.Repository, deviceRepo device.Repository, automationService *AutomationService) *SensorEventHandler {
	return &SensorEventHandler{
		automationRepo:    automationRepo,
		deviceRepo:        deviceRepo,
		automationService: automationService,
		eventChannel:      make(chan <PERSON>, 100),
		stopChannel:       make(chan bool),
		isRunning:         false,
	}
}

func (h *SensorEventHandler) Start() {
	if h.isRunning {
		log.Println("Sensor Event Handler is already running")
		return
	}

	log.Println("Starting Sensor Event Handler...")
	h.isRunning = true

	go func() {
		for {
			select {
			case event := <-h.eventChannel:
				h.handleSensorEvent(event)
			case <-h.stopChannel:
				log.Println("Stopping Sensor Event Handler...")
				h.isRunning = false
				return
			}
		}
	}()
}

func (h *SensorEventHandler) Stop() {
	if !h.isRunning {
		return
	}

	h.stopChannel <- true
}

func (h *SensorEventHandler) HandleSensorData(deviceID int, propertyID int, value string) {
	if !h.isRunning {
		log.Println("Sensor Event Handler is not running, ignoring event")
		return
	}

	event := SensorEvent{
		DeviceID:   deviceID,
		PropertyID: propertyID,
		Value:      value,
		Timestamp:  time.Now(),
	}

	select {
	case h.eventChannel <- event:
	default:
		log.Println("Event channel is full, dropping event")
	}
}

func (h *SensorEventHandler) handleSensorEvent(event SensorEvent) {
	log.Printf("Processing sensor event: Device %d, Property %d, Value %s",
		event.DeviceID, event.PropertyID, event.Value)

	// Update device status column directly (không dùng device_states table)
	if err := h.deviceRepo.UpdateDeviceStatus(event.DeviceID, event.Value); err != nil {
		log.Printf("Error updating device status: %v", err)
		return
	}

	rules, err := h.automationRepo.GetActiveConditionRules()
	if err != nil {
		log.Printf("Error getting active condition rules: %v", err)
		return
	}

	for _, rule := range rules {
		if h.isRuleRelevant(rule, event.DeviceID, event.PropertyID) {
			shouldExecute, err := h.evaluateRuleConditions(rule.RuleID)
			if err != nil {
				log.Printf("Error evaluating rule conditions for rule %d: %v", rule.RuleID, err)
				continue
			}

			if shouldExecute {
				log.Printf("Triggering rule: %s (ID: %d)", rule.Name, rule.RuleID)
				h.executeTriggeredRule(rule.RuleID, event)
			}
		}
	}
}

func (h *SensorEventHandler) isRuleRelevant(rule models.AutomationRule, deviceID, propertyID int) bool {
	for _, condition := range rule.Conditions {
		if condition.DeviceID == deviceID && condition.PropertyID != nil && *condition.PropertyID == propertyID {
			return true
		}
	}
	return false
}

func (h *SensorEventHandler) evaluateRuleConditions(ruleID int) (bool, error) {
	conditions, err := h.automationRepo.GetConditionsByRuleID(ruleID)
	if err != nil {
		return false, err
	}

	if len(conditions) == 0 {
		return false, nil
	}

	results := make([]bool, len(conditions))
	for i, condition := range conditions {
		// Always use device status column for sensor data
		device, err := h.deviceRepo.GetDeviceByID(condition.DeviceID)
		if err != nil {
			log.Printf("Error getting device %d: %v", condition.DeviceID, err)
			return false, err
		}

		currentValue := device.Status
		log.Printf("Using device status for automation condition: Device %d, Status: %s", condition.DeviceID, currentValue)

		results[i] = h.compareValues(currentValue, condition.Operator, condition.Value)

		log.Printf("Condition evaluation: Device %d, Current Status: %s, Operator: %s, Target: %s, Result: %t",
			condition.DeviceID, currentValue, condition.Operator, condition.Value, results[i])
	}

	return h.combineResults(results, conditions), nil
}

func (h *SensorEventHandler) compareValues(currentValue, operator, targetValue string) bool {
	switch operator {
	case "=":
		return currentValue == targetValue
	case "!=":
		return currentValue != targetValue
	case ">":
		return h.compareNumeric(currentValue, targetValue, ">")
	case "<":
		return h.compareNumeric(currentValue, targetValue, "<")
	case ">=":
		return h.compareNumeric(currentValue, targetValue, ">=")
	case "<=":
		return h.compareNumeric(currentValue, targetValue, "<=")
	default:
		return false
	}
}

func (h *SensorEventHandler) compareNumeric(current, target, operator string) bool {
	currentFloat, err1 := strconv.ParseFloat(current, 64)
	targetFloat, err2 := strconv.ParseFloat(target, 64)

	if err1 != nil || err2 != nil {
		switch operator {
		case ">":
			return current > target
		case "<":
			return current < target
		case ">=":
			return current >= target
		case "<=":
			return current <= target
		}
		return false
	}

	switch operator {
	case ">":
		return currentFloat > targetFloat
	case "<":
		return currentFloat < targetFloat
	case ">=":
		return currentFloat >= targetFloat
	case "<=":
		return currentFloat <= targetFloat
	}
	return false
}

func (h *SensorEventHandler) combineResults(results []bool, conditions []models.RuleCondition) bool {
	if len(results) == 0 {
		return false
	}

	if len(results) == 1 {
		return results[0]
	}

	finalResult := results[0]
	for i := 1; i < len(results); i++ {
		if conditions[i].LogicalOperator == "OR" {
			finalResult = finalResult || results[i]
		} else {
			finalResult = finalResult && results[i]
		}
	}

	return finalResult
}

func (h *SensorEventHandler) executeTriggeredRule(ruleID int, triggerEvent SensorEvent) {
	startTime := time.Now()
	err := h.automationService.ExecuteRule(ruleID)
	executionTime := int(time.Since(startTime).Milliseconds())

	status := "success"
	message := fmt.Sprintf("Rule triggered by sensor event: Device %d, Property %d, Value %s",
		triggerEvent.DeviceID, triggerEvent.PropertyID, triggerEvent.Value)

	if err != nil {
		status = "failed"
		message = fmt.Sprintf("Sensor-triggered rule execution failed: %v", err)
		log.Printf("Error executing triggered rule %d: %v", ruleID, err)
	}

	executionLog := &models.RuleExecutionLog{
		RuleID:          ruleID,
		Status:          status,
		Message:         message,
		ExecutionTimeMs: executionTime,
		TriggeredBy:     "condition",
		CreatedAt:       time.Now(),
	}

	if err := h.automationRepo.CreateExecutionLog(executionLog); err != nil {
		log.Printf("Error creating execution log: %v", err)
	}
}

func (h *SensorEventHandler) IsRunning() bool {
	return h.isRunning
}

func (h *SensorEventHandler) GetEventChannelStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running":     h.isRunning,
		"channel_length": len(h.eventChannel),
		"channel_cap":    cap(h.eventChannel),
	}
}
