package config

import (
	"log"
	"os"
	"strconv"
	"github.com/joho/godotenv"
)

type Config struct {
	ServerPort string

	DBHost     string
	DBPort     string
	DBUser     string
	DBPassword string
	DBName     string

	JWTSecret        string
	JWTExpireMinutes int

	SMTPHost     string
	SMTPPort     string
	SMTPUsername string
	SMTPPassword string
	SMTPFrom     string

	MQTTBroker   string
	MQTTPort     int
	MQTTClientID string
	MQTTUsername string
	MQTTPassword string
}

func LoadConfig() *Config {
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found, using environment variables or defaults")
	}

	jwtExpireMinutes := 60
	if jwtExpireStr := os.Getenv("JWT_EXPIRE_MINUTES"); jwtExpireStr != "" {
		if minutes, err := strconv.Atoi(jwtExpireStr); err == nil {
			jwtExpireMinutes = minutes
		} else {
			log.Printf("Warning: Invalid JWT_EXPIRE_MINUTES value '%s', using default 60", jwtExpireStr)
		}
	}

	mqttPort := 1883
	if mqttPortStr := os.Getenv("MQTT_PORT"); mqttPortStr != "" {
		if port, err := strconv.Atoi(mqttPortStr); err == nil {
			mqttPort = port
		} else {
			log.Printf("Warning: Invalid MQTT_PORT value '%s', using default 1883", mqttPortStr)
		}
	}

	config := &Config{
		ServerPort: getEnvOrDefault("SERVER_PORT", "8080"),

		DBHost:     getEnvOrDefault("DB_HOST", "localhost"),
		DBPort:     getEnvOrDefault("DB_PORT", "3306"),
		DBUser:     getEnvOrDefault("DB_USER", "root"),
		DBPassword: getEnvOrDefault("DB_PASSWORD", ""),
		DBName:     getEnvOrDefault("DB_NAME", "smart_home"),

		JWTSecret:        getEnvOrDefault("JWT_SECRET", ""),
		JWTExpireMinutes: jwtExpireMinutes,

		SMTPHost:     getEnvOrDefault("SMTP_HOST", "smtp.gmail.com"),
		SMTPPort:     getEnvOrDefault("SMTP_PORT", "587"),
		SMTPUsername: getEnvOrDefault("SMTP_USERNAME", ""),
		SMTPPassword: getEnvOrDefault("SMTP_PASSWORD", ""),
		SMTPFrom:     getEnvOrDefault("SMTP_FROM", ""),

		MQTTBroker:   getEnvOrDefault("MQTT_BROKER", "localhost"),
		MQTTPort:     mqttPort,
		MQTTClientID: getEnvOrDefault("MQTT_CLIENT_ID", "smart_home_api"),
		MQTTUsername: getEnvOrDefault("MQTT_USERNAME", ""),
		MQTTPassword: getEnvOrDefault("MQTT_PASSWORD", ""),
	}

	validateConfig(config)

	return config
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func validateConfig(config *Config) {
	required := map[string]string{
		"DB_PASSWORD":   config.DBPassword,
		"JWT_SECRET":    config.JWTSecret,
		"SMTP_USERNAME": config.SMTPUsername,
		"SMTP_PASSWORD": config.SMTPPassword,
		"SMTP_FROM":     config.SMTPFrom,
	}

	for key, value := range required {
		if value == "" {
			log.Fatalf("Required environment variable %s is not set", key)
		}
	}

	if len(config.JWTSecret) < 32 {
		log.Fatal("JWT_SECRET must be at least 32 characters long")
	}
}
