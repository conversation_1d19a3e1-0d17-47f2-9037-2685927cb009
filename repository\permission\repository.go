package permission

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
)

type Repository interface {
	CheckDevicePermission(userID int, deviceID int) (bool, error)
	CheckAreaPermission(userID int, homeID int) (bool, error)
	GetUserPermissions(userID int) ([]models.Permission, error)
	AddDevicePermission(permission *models.Permission) error
	AddAreaPermission(permission *models.Permission) error
	RemoveDevicePermission(userID int, deviceID int) error
	RemoveAreaPermission(userID int, areaID int) error
	UpdatePermission(permissionID int, canView, canControl bool) error
	GetPermissionByID(permissionID int) (*models.Permission, error)
	GetUserRoles(userID int) ([]models.Role, error)

	CheckDeviceControlPermission(userID int, deviceID int) (bool, error)
	CheckDeviceViewPermission(userID int, deviceID int) (bool, error)
	CheckDeviceConfigPermission(userID int, deviceID int) (bool, error)
	CheckAreaViewPermission(userID int, areaID int) (bool, error)
	CheckHomeViewPermission(userID int, homeID int) (bool, error)

	GetPermissionsByHome(homeID int) ([]models.Permission, error)
	GetPermissionsByHomeWithFilter(homeID int, filterType string) ([]models.Permission, error)
	CheckHomePermission(userID uint, homeID uint) error

	GetUserRoleInHome(userID int, homeID int) (int, error)
	UpdateUserRoleInHome(userID int, homeID int, roleID int) error
	CheckUserCanKickUser(kickerID int, targetID int, homeID int) (bool, error)
	CheckHomeAdminPermission(userID int, homeID int) (bool, error)
}
