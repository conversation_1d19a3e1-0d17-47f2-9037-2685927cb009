package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/automation"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"
)

type SchedulerService struct {
	automationRepo    automation.Repository
	automationService *AutomationService
	ticker            *time.Ticker
	stopChannel       chan bool
	isRunning         bool
}

func NewSchedulerService(automationRepo automation.Repository, automationService *AutomationService) *SchedulerService {
	return &SchedulerService{
		automationRepo:    automationRepo,
		automationService: automationService,
		stopChannel:       make(chan bool),
		isRunning:         false,
	}
}

func (s *SchedulerService) Start() {
	if s.isRunning {
		log.Println("Scheduler is already running")
		return
	}

	log.Println("Starting Automation Scheduler...")
	s.isRunning = true

	s.ticker = time.NewTicker(1 * time.Minute)

	go func() {
		for {
			select {
			case <-s.ticker.C:
				s.checkAndExecuteScheduledRules()
			case <-s.stopChannel:
				log.Println("Stopping Automation Scheduler...")
				s.ticker.Stop()
				s.isRunning = false
				return
			}
		}
	}()
}

func (s *SchedulerService) Stop() {
	if !s.isRunning {
		return
	}

	s.stopChannel <- true
}

func (s *SchedulerService) checkAndExecuteScheduledRules() {

	schedules, err := s.automationRepo.GetSchedulesDueForExecution()
	if err != nil {
		log.Printf("Error getting schedules due for execution: %v", err)
		return
	}

	for _, schedule := range schedules {
		if schedule.RuleID == 0 {
			continue
		}

		log.Printf("Executing scheduled rule: %d (ID: %d)", schedule.RuleID, schedule.RuleID)

		startTime := time.Now()

		err := s.executeScheduledRule(schedule.RuleID)

		executionTime := int(time.Since(startTime).Milliseconds())

		status := "success"
		message := "Rule executed successfully"
		if err != nil {
			status = "failed"
			message = fmt.Sprintf("Rule execution failed: %v", err)
			log.Printf("Error executing rule %d: %v", schedule.RuleID, err)
		}

		executionLog := &models.RuleExecutionLog{
			RuleID:          schedule.RuleID,
			Status:          status,
			Message:         message,
			ExecutionTimeMs: executionTime,
			TriggeredBy:     "schedule",
			CreatedAt:       time.Now(),
		}

		if err := s.automationRepo.CreateExecutionLog(executionLog); err != nil {
			log.Printf("Error creating execution log: %v", err)
		}

		nextRun := s.calculateNextRun(schedule.ScheduleExpression, schedule.Timezone)
		if err := s.automationRepo.UpdateScheduleNextRun(schedule.RuleID, nextRun); err != nil {
			log.Printf("Error updating next run time: %v", err)
		}
	}
}

func (s *SchedulerService) executeScheduledRule(ruleID int) error {
	return s.automationService.ExecuteRule(ruleID)
}

func (s *SchedulerService) calculateNextRun(cronExpression, timezone string) *time.Time {

	parts := strings.Fields(cronExpression)
	if len(parts) != 5 {
		nextRun := time.Now().Add(time.Hour)
		return &nextRun
	}

	now := time.Now()

	minute := s.parseCronField(parts[0], 0, 59, now.Minute())

	hour := s.parseCronField(parts[1], 0, 23, now.Hour())

	var nextRun time.Time

	if minute > now.Minute() {
		nextRun = time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), minute, 0, 0, now.Location())
	} else if hour > now.Hour() {
		nextRun = time.Date(now.Year(), now.Month(), now.Day(), hour, minute, 0, 0, now.Location())
	} else {
		tomorrow := now.AddDate(0, 0, 1)
		nextRun = time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), hour, minute, 0, 0, now.Location())
	}

	return &nextRun
}

func (s *SchedulerService) parseCronField(field string, min, max, current int) int {
	if field == "*" {
		return current
	}

	if value, err := strconv.Atoi(field); err == nil {
		if value >= min && value <= max {
			return value
		}
	}

	if strings.Contains(field, "-") {
		parts := strings.Split(field, "-")
		if len(parts) == 2 {
			start, err1 := strconv.Atoi(parts[0])
			end, err2 := strconv.Atoi(parts[1])
			if err1 == nil && err2 == nil && start >= min && end <= max {
				if current >= start && current <= end {
					return current
				}
				return start
			}
		}
	}

	if strings.Contains(field, ",") {
		parts := strings.Split(field, ",")
		for _, part := range parts {
			if value, err := strconv.Atoi(strings.TrimSpace(part)); err == nil {
				if value >= min && value <= max && value >= current {
					return value
				}
			}
		}
		if value, err := strconv.Atoi(strings.TrimSpace(parts[0])); err == nil {
			if value >= min && value <= max {
				return value
			}
		}
	}

	return current
}

func (s *SchedulerService) IsRunning() bool {
	return s.isRunning
}

func (s *SchedulerService) GetSchedulerStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running":     s.isRunning,
		"check_interval": "1 minute",
		"last_check":     time.Now().Format("2006-01-02 15:04:05"),
	}
}

func (s *SchedulerService) ForceExecuteRule(ruleID int) error {
	log.Printf("Force executing rule: %d", ruleID)

	startTime := time.Now()
	err := s.automationService.ExecuteRule(ruleID)
	executionTime := int(time.Since(startTime).Milliseconds())

	status := "success"
	message := "Rule executed manually"
	if err != nil {
		status = "failed"
		message = fmt.Sprintf("Manual rule execution failed: %v", err)
	}

	executionLog := &models.RuleExecutionLog{
		RuleID:          ruleID,
		Status:          status,
		Message:         message,
		ExecutionTimeMs: executionTime,
		TriggeredBy:     "manual",
		CreatedAt:       time.Now(),
	}

	if logErr := s.automationRepo.CreateExecutionLog(executionLog); logErr != nil {
		log.Printf("Error creating execution log: %v", logErr)
	}

	return err
}
