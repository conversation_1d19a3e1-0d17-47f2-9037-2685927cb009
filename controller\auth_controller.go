package controller

import (
	"bytes"
	"fmt"
	"io"
	"net/http"

	"dh52110724-api-quan-ly-nha-thong-minh/middleware"
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"
	"log"

	"github.com/gin-gonic/gin"
)

type Authcontrollers struct {
	authService *service.AuthService
}

func NewAuthcontrollers(authService *service.AuthService) *Authcontrollers {
	return &Authcontrollers{
		authService: authService,
	}
}

func (h *Authcontrollers) Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  false,
			"message": fmt.Sprintf("<PERSON><PERSON> liệu không hợp lệ: %v", err.<PERSON>r()),
		})
		return
	}

	if err := h.authService.Register(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  false,
			"message": err.Error(),
		})
		return
	}

	responseData := gin.H{
		"email": req.Email,
	}
	utils.SuccessResponse(c, http.StatusOK, "Đăng ký thành công! Vui lòng kiểm tra email để xác thực tài khoản. Nhà thông minh sẽ được tạo tự động sau khi đăng nhập lần đầu", responseData)
}

func (ac *Authcontrollers) Login(c *gin.Context) {

	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Không đọc được body")
		return
	}

	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	resp, err := ac.authService.Login(req.Email, req.Password)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Đăng nhập thành công, vui lòng xác thực OTP", resp)
}

func (h *Authcontrollers) RequestOTP(c *gin.Context) {
	var req struct {
		Email string `json:"email" binding:"required,email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email không hợp lệ")
		return
	}

	if err := h.authService.RequestOTP(req.Email); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Đã gửi mã OTP thành công", nil)
}

func (h *Authcontrollers) VerifyOTP(c *gin.Context) {
	var req models.VerifyOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	log.Printf("=== DEBUG VERIFY OTP ===")
	log.Printf("Email: %s", req.Email)
	log.Printf("Received OTP: %s", req.OTP)
	log.Printf("OTP Length: %d", len(req.OTP))

	response, err := h.authService.VerifyOTP(req.Email, req.OTP)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	log.Printf("OTP verification successful for email: %s", req.Email)

	user, err := h.authService.GetUserByEmail(req.Email)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Không tìm thấy thông tin người dùng")
		return
	}

	if !user.IsVerified {
		user.IsVerified = true
		if err := h.authService.UpdateUser(user); err != nil {
			log.Printf("Warning: Could not update user verification status: %v", err)
		}
	}

	c.JSON(http.StatusOK, response)
}

func (h *Authcontrollers) VerifyEmailRegistration(c *gin.Context) {
	var req models.VerifyOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	log.Printf("=== DEBUG VERIFY EMAIL REGISTRATION ===")
	log.Printf("Email: %s", req.Email)
	log.Printf("Received OTP: %s", req.OTP)

	if err := h.authService.VerifyEmail(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	log.Printf("Email verification successful for: %s", req.Email)

	utils.SuccessResponse(c, http.StatusOK, "Xác thực email thành công! Bạn có thể đăng nhập ngay bây giờ", nil)
}

func (h *Authcontrollers) ResendVerificationOTP(c *gin.Context) {
	var req models.ResendOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	user, err := h.authService.GetUserByEmail(req.Email)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Không tìm thấy tài khoản với email này")
		return
	}

	if user.IsVerified {
		utils.ErrorResponse(c, http.StatusBadRequest, "Tài khoản đã được xác thực trước đó")
		return
	}

	if err := h.authService.RequestOTP(req.Email); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Mã xác thực mới đã được gửi đến email của bạn", nil)
}

func (h *Authcontrollers) VerifyEmail(c *gin.Context) {
	var req models.VerifyOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	if err := h.authService.VerifyEmail(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Xác thực email thành công", nil)
}

func (c *Authcontrollers) UpdateProfile(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	var req struct {
		Email       string `json:"email"`
		FullName    string `json:"full_name"`
		PhoneNumber string `json:"phone_number"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	var phonePtr *string
	if req.PhoneNumber != "" {
		phonePtr = &req.PhoneNumber
	}

	updateReq := &models.UpdateProfileRequest{
		Email:       req.Email,
		FullName:    req.FullName,
		PhoneNumber: phonePtr,
	}
	if err := c.authService.UpdateProfile(int(userID.(uint)), updateReq); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Cập nhật thông tin thành công", nil)
}

func (h *Authcontrollers) ForgotPassword(c *gin.Context) {
	var req models.ForgotPasswordRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email không hợp lệ")
		return
	}

	if err := h.authService.ForgotPassword(req.Email); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Mã OTP đã được gửi đến email của bạn", nil)
}

func (h *Authcontrollers) ResetPassword(c *gin.Context) {
	var req models.ResetPasswordRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	if err := h.authService.ResetPassword(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Đặt lại mật khẩu thành công", nil)
}

func (h *Authcontrollers) ChangePassword(c *gin.Context) {
	var req models.ChangePasswordRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
		return
	}

	userIDInt, ok := userID.(uint)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xử lý thông tin người dùng")
		return
	}

	if err := h.authService.ChangePassword(int(userIDInt), &req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Đổi mật khẩu thành công", nil)
}

func (h *Authcontrollers) GetProfile(c *gin.Context) {
	email, exists := middleware.GetEmail(c)
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin xác thực")
		return
	}

	user, err := h.authService.GetUserByEmail(email)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy thông tin người dùng")
		return
	}
	utils.SuccessResponse(c, http.StatusOK, "Lấy thông tin người dùng thành công", user)

}
func (h *Authcontrollers) ResendOTP(c *gin.Context) {
	var req models.ResendOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email không hợp lệ")
		return
	}

	otp := utils.GetOTPStore().GenerateOTP(req.Email)
	if err := utils.SendOTPEmail(req.Email, otp); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Không thể gửi email OTP")
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  true,
		"message": "Đã gửi lại mã OTP mới",
	})
}
