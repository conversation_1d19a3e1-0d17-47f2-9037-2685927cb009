package device

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type mysqlDeviceRepository struct {
	db *gorm.DB
}

func NewPostgresRepository(db *gorm.DB) Repository {
	return &mysqlDeviceRepository{
		db: db,
	}
}

func (r *mysqlDeviceRepository) CreateDevice(device *models.Device) (int, error) {
	if err := r.db.Create(device).Error; err != nil {
		return 0, err
	}
	return device.DeviceID, nil
}

func (r *mysqlDeviceRepository) GetDeviceByID(deviceID int) (*models.Device, error) {
	var device models.Device
	err := r.db.Preload("DeviceType").Preload("Area").Preload("NetworkInfo").
		Where("device_id = ?", deviceID).First(&device).Error
	if err != nil {
		return nil, err
	}
	return &device, nil
}

func (r *mysqlDeviceRepository) GetAllDevices() ([]models.Device, error) {
	var devices []models.Device

	result := r.db.
		Preload("DeviceType").
		Preload("Area").
		Find(&devices)

	return devices, result.Error
}

func (r *mysqlDeviceRepository) GetDevicesByUserPermission(userID int) ([]models.Device, error) {
	var devices []models.Device

	result := r.db.Table("devices").
		Joins("JOIN permissions ON devices.device_id = permissions.device_id").
		Where("permissions.user_id = ? AND permissions.can_view = true", userID).
		Preload("DeviceType").
		Preload("Area").
		Find(&devices)

	return devices, result.Error
}

func (r *mysqlDeviceRepository) GetDevicesByAreaID(areaID int) ([]models.Device, error) {
	var devices []models.Device
	err := r.db.Where("area_id = ?", areaID).
		Preload("DeviceType").Preload("Area").
		Find(&devices).Error
	return devices, err
}

func (r *mysqlDeviceRepository) GetDevicesWithoutHome() ([]models.Device, error) {
	var devices []models.Device
	err := r.db.Where("home_id IS NULL").
		Preload("DeviceType").
		Find(&devices).Error
	return devices, err
}

func (r *mysqlDeviceRepository) UpdateDevice(device *models.Device) error {
	return r.db.Model(device).
		Where("device_id = ?", device.DeviceID).
		Updates(map[string]interface{}{
			"status":     device.Status,
			"updated_at": device.UpdatedAt,
		}).Error
}

func (r *mysqlDeviceRepository) UpdateDeviceArea(deviceID int, areaID *int) error {
	updates := map[string]interface{}{
		"area_id":    areaID,
		"updated_at": time.Now(),
	}
	return r.db.Model(&models.Device{}).
		Where("device_id = ?", deviceID).
		Updates(updates).Error
}

func (r *mysqlDeviceRepository) UpdateDeviceStatus(deviceID int, status string) error {
	return r.db.Model(&models.Device{}).
		Where("device_id = ?", deviceID).
		Update("status", status).Error
}

func (r *mysqlDeviceRepository) DeleteDevice(deviceID int) error {
	return r.db.Where("device_id = ?", deviceID).Delete(&models.Device{}).Error
}

func (r *mysqlDeviceRepository) RemoveDeviceFromHome(deviceID int) error {
	return r.db.Model(&models.Device{}).
		Where("device_id = ?", deviceID).
		Update("home_id", nil).Error
}

func (r *mysqlDeviceRepository) DeviceExists(deviceID int) bool {
	var count int64
	r.db.Model(&models.Device{}).Where("device_id = ?", deviceID).Count(&count)
	return count > 0
}

func (r *mysqlDeviceRepository) DeviceTypeExists(deviceTypeID int) bool {
	var count int64
	r.db.Model(&models.DeviceType{}).Where("device_type_id = ?", deviceTypeID).Count(&count)
	return count > 0
}

func (r *mysqlDeviceRepository) GetDeviceTypeByID(deviceTypeID int) (*models.DeviceType, error) {
	var deviceType models.DeviceType
	err := r.db.Where("device_type_id = ?", deviceTypeID).First(&deviceType).Error
	if err != nil {
		return nil, err
	}
	return &deviceType, nil
}

func (r *mysqlDeviceRepository) GetAllDeviceTypes() ([]models.DeviceType, error) {
	var deviceTypes []models.DeviceType
	err := r.db.Find(&deviceTypes).Error
	return deviceTypes, err
}

func (r *mysqlDeviceRepository) AreaExists(areaID int) bool {
	var count int64
	r.db.Model(&models.Area{}).Where("area_id = ?", areaID).Count(&count)
	return count > 0
}

func (r *mysqlDeviceRepository) GetAreaByID(areaID int) (*models.Area, error) {
	var area models.Area
	err := r.db.Where("area_id = ?", areaID).First(&area).Error
	if err != nil {
		return nil, err
	}
	return &area, nil
}

func (r *mysqlDeviceRepository) PropertyExists(propertyID int) bool {
	var count int64
	r.db.Model(&models.DeviceProperty{}).Where("property_id = ?", propertyID).Count(&count)
	return count > 0
}

func (r *mysqlDeviceRepository) GetPropertyByID(propertyID int) (*models.DeviceProperty, error) {
	var property models.DeviceProperty
	err := r.db.Where("property_id = ?", propertyID).First(&property).Error
	if err != nil {
		return nil, err
	}
	return &property, nil
}

func (r *mysqlDeviceRepository) GetPropertiesByDeviceType(deviceTypeID int) ([]models.DeviceProperty, error) {
	var properties []models.DeviceProperty
	err := r.db.Where("device_type_id = ?", deviceTypeID).Find(&properties).Error
	return properties, err
}

func (r *mysqlDeviceRepository) CreateProperty(property *models.DeviceProperty) (int, error) {
	if err := r.db.Create(property).Error; err != nil {
		return 0, err
	}
	return property.PropertyID, nil
}

func (r *mysqlDeviceRepository) UpdateProperty(property *models.DeviceProperty) error {
	return r.db.Save(property).Error
}

func (r *mysqlDeviceRepository) UpdateDeviceState(deviceID int, propertyID int, value string) error {
	state := &models.DeviceState{
		DeviceID:   deviceID,
		PropertyID: propertyID,
		Value:      value,
		Timestamp:  time.Now(),
	}

	if err := r.db.Save(state).Error; err != nil {
		return err
	}

	return nil
}

func (r *mysqlDeviceRepository) GetDeviceStates(deviceID int) ([]models.DeviceState, error) {
	var states []models.DeviceState
	err := r.db.Where("device_id = ?", deviceID).
		Preload("Property").
		Find(&states).Error
	return states, err
}

func (r *mysqlDeviceRepository) GetCurrentPropertyValue(deviceID int, propertyID int) (string, error) {
	var state models.DeviceState
	err := r.db.Where("device_id = ? AND property_id = ?", deviceID, propertyID).
		Order("timestamp DESC").
		First(&state).Error
	if err != nil {
		return "", err
	}
	return state.Value, nil
}


func (r *mysqlDeviceRepository) CreateCommand(command *models.DeviceCommand) error {
	return r.db.Create(command).Error
}

func (r *mysqlDeviceRepository) UpdateCommandStatus(commandID int, status string) error {
	return r.db.Model(&models.DeviceCommand{}).
		Where("command_id = ?", commandID).
		Updates(map[string]interface{}{
			"status":      status,
			"executed_at": time.Now(),
		}).Error
}

func (r *mysqlDeviceRepository) GetDeviceCommandHistory(deviceID int, limit int) ([]models.DeviceCommand, error) {
	var commands []models.DeviceCommand
	query := r.db.Where("device_id = ?", deviceID).Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&commands).Error
	return commands, err
}

func (r *mysqlDeviceRepository) GetConnectionTypeByID(connectionTypeID int) (*models.ConnectionType, error) {
	var connectionType models.ConnectionType
	err := r.db.Where("connection_type_id = ?", connectionTypeID).First(&connectionType).Error
	if err != nil {
		return nil, err
	}
	return &connectionType, nil
}

func (r *mysqlDeviceRepository) GetAllConnectionTypes() ([]models.ConnectionType, error) {
	var connectionTypes []models.ConnectionType
	err := r.db.Find(&connectionTypes).Error
	return connectionTypes, err
}



func (r *mysqlDeviceRepository) IsDeviceUsedInRules(deviceID int) (bool, error) {
	var count int64

	err := r.db.Model(&models.RuleCondition{}).
		Where("device_id = ?", deviceID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	if count > 0 {
		return true, nil
	}

	err = r.db.Model(&models.RuleAction{}).
		Where("device_id = ?", deviceID).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *mysqlDeviceRepository) GetDeviceByUniqueIdentifier(uniqueIdentifier string) (*models.Device, error) {
	var device models.Device
	err := r.db.Where("unique_identifier = ?", uniqueIdentifier).First(&device).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("thiết bị chưa được đăng ký trong hệ thống")
	}
	return &device, nil
}

func (r *mysqlDeviceRepository) CreateNetworkInfo(networkInfo *models.DeviceNetworkInfo) error {
	return r.db.Create(networkInfo).Error
}





func (r *mysqlDeviceRepository) GetPropertyIDByName(deviceID int, propertyName string) (int, error) {
	var property models.DeviceProperty
	err := r.db.Joins("JOIN property_templates ON device_properties.template_id = property_templates.template_id").
		Where("device_properties.device_id = ? AND property_templates.property_name = ?", deviceID, propertyName).
		First(&property).Error
	if err != nil {
		return 0, err
	}
	return property.PropertyID, nil
}

func (r *mysqlDeviceRepository) GetPropertyIDByTemplate(deviceID int, templateID int) (int, error) {
	var property models.DeviceProperty
	err := r.db.Where("device_id = ? AND template_id = ?", deviceID, templateID).
		First(&property).Error
	if err != nil {
		return 0, err
	}
	return property.PropertyID, nil
}

func (r *mysqlDeviceRepository) UpdateDeviceOnlineStatus(deviceID int, isOnline bool) error {
	return r.db.Model(&models.Device{}).Where("device_id = ?", deviceID).Update("is_online", isOnline).Error
}

func (r *mysqlDeviceRepository) GetRealTimeDeviceStatus(deviceID int) (*models.RealTimeDeviceStatus, error) {
	var device models.Device
	err := r.db.Preload("DeviceType").Preload("Area").Where("device_id = ?", deviceID).First(&device).Error
	if err != nil {
		return nil, err
	}

	areaName := "No Area"
	if device.Area != nil {
		areaName = device.Area.Name
	}

	deviceTypeName := "Unknown Type"
	if device.DeviceType != nil {
		deviceTypeName = device.DeviceType.Name
	}

	return &models.RealTimeDeviceStatus{
		DeviceID:    device.DeviceID,
		Name:        device.Name,
		DeviceType:  deviceTypeName,
		AreaName:    areaName,
		IsOnline:    device.IsOnline,
		Value:       device.Status,
		LastUpdated: device.UpdatedAt,
	}, nil
}

func (r *mysqlDeviceRepository) GetAllRealTimeDeviceStatus(userID int) ([]models.RealTimeDeviceStatus, error) {
	devices, err := r.GetDevicesByUserPermission(userID)
	if err != nil {
		return nil, err
	}

	var statuses []models.RealTimeDeviceStatus
	for _, device := range devices {
		status, err := r.GetRealTimeDeviceStatus(device.DeviceID)
		if err != nil {
			continue
		}
		statuses = append(statuses, *status)
	}

	return statuses, nil
}

func (r *mysqlDeviceRepository) UpdateDeviceProperty(deviceID int, propertyName string, value interface{}) error {
	propertyID, err := r.GetPropertyIDByName(deviceID, propertyName)
	if err != nil {
		return err
	}

	valueStr := fmt.Sprintf("%v", value)

	return r.UpdateDeviceState(deviceID, propertyID, valueStr)
}

func (r *mysqlDeviceRepository) ExecuteBatchControl(commands []models.DeviceCommand) ([]models.BatchControlResult, error) {
	var results []models.BatchControlResult

	for _, command := range commands {
		result := models.BatchControlResult{
			DeviceID: command.DeviceID,
			Success:  false,
		}

		if err := r.CreateCommand(&command); err != nil {
			result.Error = err.Error()
			result.Message = "Không thể tạo command log"
		} else {
			result.Success = true
			result.Message = "Command đã được gửi thành công"
		}

		results = append(results, result)
	}

	return results, nil
}

func (r *mysqlDeviceRepository) GetDeviceProperties(deviceID int) ([]models.DeviceProperty, error) {
	var properties []models.DeviceProperty
	err := r.db.Where("device_id = ?", deviceID).Find(&properties).Error
	return properties, err
}

func (r *mysqlDeviceRepository) GetHomeIDByAreaID(areaID int) (uint, error) {
	var area models.Area

	result := r.db.Where("area_id = ?", areaID).First(&area)
	if result.Error != nil {
		return 0, fmt.Errorf("không tìm thấy area với id %v", areaID)
	}

	return uint(area.HomeID), nil
}

func (r *mysqlDeviceRepository) GetDeviceCommands(deviceID int) ([]models.DeviceCommand, error) {
	var commands []models.DeviceCommand
	err := r.db.Where("device_id = ?", deviceID).Order("created_at DESC").Find(&commands).Error
	return commands, err
}

func (r *mysqlDeviceRepository) GetDevicesByHomeID(homeID int) ([]models.Device, error) {
	var devices []models.Device
	result := r.db.Where("home_id = ?", homeID).
		Preload("DeviceType").
		Preload("Area").
		Preload("NetworkInfo").
		Find(&devices)
	return devices, result.Error
}

func (r *mysqlDeviceRepository) GetDevicesByTypeInHome(homeID int, deviceTypeID int) ([]models.Device, error) {
	var devices []models.Device
	result := r.db.Where("home_id = ? AND device_type_id = ?", homeID, deviceTypeID).
		Preload("DeviceType").
		Preload("Area").
		Preload("NetworkInfo").
		Find(&devices)
	return devices, result.Error
}

func (r *mysqlDeviceRepository) GetDevicesInHomeWithoutArea(homeID int) ([]models.Device, error) {
	var devices []models.Device
	result := r.db.Where("home_id = ? AND area_id IS NULL", homeID).
		Preload("DeviceType").
		Find(&devices)
	return devices, result.Error
}

func (r *mysqlDeviceRepository) GetAllRealTimeDeviceStatusByHome(homeID int) ([]models.RealTimeDeviceStatus, error) {
	devices, err := r.GetDevicesByHomeID(homeID)
	if err != nil {
		return nil, fmt.Errorf("lỗi khi lấy devices: %v", err)
	}

	statuses, err := r.mapToRealTimeStatus(devices)
	if err != nil {
		return nil, fmt.Errorf("lỗi khi map status: %v", err)
	}

	return statuses, nil
}

func (r *mysqlDeviceRepository) AreaExistsInHome(areaID, homeID int) bool {
	var count int64
	r.db.Model(&models.Area{}).Where("area_id = ? AND home_id = ?", areaID, homeID).Count(&count)
	return count > 0
}

func (r *mysqlDeviceRepository) mapToRealTimeStatus(devices []models.Device) ([]models.RealTimeDeviceStatus, error) {
	var statuses []models.RealTimeDeviceStatus

	for _, device := range devices {
		areaName := "No Area"
		if device.Area != nil {
			areaName = device.Area.Name
		}

		deviceTypeName := "Unknown Type"
		if device.DeviceType != nil {
			deviceTypeName = device.DeviceType.Name
		}

		status := models.RealTimeDeviceStatus{
			DeviceID:    device.DeviceID,
			Name:        device.Name,
			IsOnline:    device.IsOnline,
			AreaName:    areaName,
			DeviceType:  deviceTypeName,
			LastUpdated: device.UpdatedAt,
			Value:       device.Status,
		}

		statuses = append(statuses, status)
	}

	return statuses, nil
}

func (r *mysqlDeviceRepository) IsDeviceRegisteredInHome(mac string, homeID int) (bool, error) {
	var count int64
	err := r.db.
		Table("devices d").
		Joins("JOIN device_network_info dni ON dni.device_id = d.device_id").
		Joins("JOIN areas a ON a.area_id = d.area_id").
		Where("dni.mac_address = ? AND a.home_id = ?", mac, homeID).
		Count(&count).Error
	return count > 0, err
}

func (r *mysqlDeviceRepository) GetConnectionTypes() ([]models.ConnectionType, error) {
	var result []models.ConnectionType

	query := `
		SELECT DISTINCT ct.*
		FROM devices d
		INNER JOIN connection_types ct ON d.connection_type_id = ct.connection_type_id
		INNER JOIN areas a ON d.area_id = a.area_id
	`

	err := r.db.Raw(query).Scan(&result).Error
	if err != nil {
		return nil, fmt.Errorf("lỗi khi truy vấn loại kết nối trong home: %w", err)
	}

	return result, nil
}

func (r *mysqlDeviceRepository) CheckDeviceExists(uniqueIdentifier string) (bool, error) {
	var count int64
	err := r.db.Model(&models.Device{}).
		Where("unique_identifier = ?", uniqueIdentifier).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}
