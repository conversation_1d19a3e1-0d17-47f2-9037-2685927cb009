package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/user"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"
	"errors"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"time"
	"unicode"

	"golang.org/x/crypto/bcrypt"
)

type AuthService struct {
	repo        user.Repository
	homeService *HomeService
}

func NewAuthService(repo user.Repository) *AuthService {
	return &AuthService{repo: repo}
}

func (s *AuthService) SetHomeService(homeService *HomeService) {
	s.homeService = homeService
}

func (s *AuthService) Register(req *models.RegisterRequest) error {
	if req == nil {
		return errors.New("thông tin đăng ký không hợp lệ")
	}

	if err := s.validatePassword(req.Password); err != nil {
		return err
	}

	exists, err := s.repo.CheckEmailExists(req.Email)
	if err != nil {
		log.Printf("Error checking email existence: %v", err)
		return errors.New("lỗi hệ thống, vui lòng thử lại sau")
	}
	if exists {
		return errors.New("email đã được sử dụng")
	}

	hash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Error hashing password: %v", err)
		return errors.New("lỗi hệ thống khi xử lý mật khẩu")
	}

	user := &models.User{
		PasswordHash: string(hash),
		Email:        req.Email,
		PhoneNumber:  nil,
		FullName:     "",
		IsVerified:   false,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.repo.CreateUser(user); err != nil {
		log.Printf("Error creating user: %v", err)
		return errors.New("không thể tạo tài khoản")
	}

	otp := utils.GetOTPStore().GenerateOTP(user.Email)

	if err := utils.SendAccountVerificationEmail(user.Email, user.FullName, otp); err != nil {
		log.Printf("Failed to send account verification email: %v", err)
		s.repo.DeleteUser(user.UserID)
		return errors.New("không thể gửi email xác thực tài khoản, vui lòng thử lại")
	}

	log.Printf("User %d registered successfully, verification OTP sent to %s", user.UserID, user.Email)
	return nil
}

func (s *AuthService) Login(email, password string) (*models.LoginResponse, error) {
	user, err := s.repo.GetUserByEmail(email)
	if err != nil {
		return nil, errors.New("email hoặc mật khẩu không đúng")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, errors.New("email hoặc mật khẩu không đúng")
	}

	if !user.IsVerified {
		return nil, errors.New("tài khoản chưa được xác thực. Vui lòng kiểm tra email để xác thực tài khoản")
	}

	utils.GetOTPStore().ClearOTP(user.Email)
	otp := utils.GetOTPStore().GenerateOTP(user.Email)

	if err := utils.SendOTPEmail(user.Email, otp); err != nil {
		log.Printf("Failed to send OTP email during login: %v", err)
		return nil, errors.New("không thể gửi mã OTP")
	}

	return &models.LoginResponse{
		Message: "Vui lòng kiểm tra email để lấy mã OTP",
		Email:   user.Email,
	}, nil
}

func (s *AuthService) UpdateProfile(userID int, req *models.UpdateProfileRequest) error {
	user, err := s.repo.GetUserByID(userID)
	if err != nil {
		return errors.New("không tìm thấy tài khoản")
	}

	if req.Email != "" && req.Email != user.Email {
		exists, err := s.repo.CheckEmailExists(req.Email)
		if err != nil {
			return err
		}
		if exists {
			return errors.New("email đã được sử dụng")
		}
		user.Email = req.Email
		user.IsVerified = false
	}

	if req.PhoneNumber != nil && *req.PhoneNumber != "" {
		currentPhone := ""
		if user.PhoneNumber != nil {
			currentPhone = *user.PhoneNumber
		}

		if *req.PhoneNumber != currentPhone {
			if err := s.validatePhoneNumber(*req.PhoneNumber); err != nil {
				return err
			}

			exists, err := s.repo.CheckPhoneExists(*req.PhoneNumber)
			if err != nil {
				return err
			}
			if exists {
				return errors.New("số điện thoại đã được sử dụng")
			}
			user.PhoneNumber = req.PhoneNumber
		}
	}

	if req.FullName != "" {
		user.FullName = req.FullName
	}

	user.UpdatedAt = time.Now()

	if err := s.repo.UpdateUser(user); err != nil {
		return errors.New("không thể cập nhật thông tin")
	}

	return nil
}

func (s *AuthService) GetUserByID(userID int) (*models.User, error) {
	return s.repo.GetUserByID(userID)
}

func (s *AuthService) RequestOTP(email string) error {
	exists, err := s.repo.CheckEmailExists(email)
	if err != nil {
		return errors.New("lỗi hệ thống, vui lòng thử lại")
	}
	if !exists {
		return errors.New("không tìm thấy tài khoản với email này")
	}

	otp := utils.GetOTPStore().GenerateOTP(email)

	if err := utils.SendOTPEmail(email, otp); err != nil {
		log.Printf("Failed to send OTP email: %v", err)
		return errors.New("không thể gửi mã OTP, vui lòng thử lại")
	}

	return nil
}

func (s *AuthService) VerifyOTP(email, otp string) (*models.VerifyOTPResponse, error) {
	user, err := s.repo.GetUserByEmail(email)
	if err != nil {
		return nil, errors.New("không tìm thấy tài khoản")
	}

	userID := fmt.Sprintf("%d", user.UserID)

	verified, token, err := utils.GetOTPStore().VerifyOTP(email, otp, &userID)
	if err != nil {
		return nil, err
	}
	if !verified {
		return nil, errors.New("mã OTP không hợp lệ hoặc đã hết hạn")
	}

	now := time.Now()
	user.LastLogin = &now
	user.UpdatedAt = now
	if err := s.repo.UpdateUser(user); err != nil {
		log.Printf("Failed to update last login: %v", err)
	}


	return &models.VerifyOTPResponse{
		Message: "Đăng nhập thành công",
		Success: true,
		Token:   token,
		User: models.UserResponse{
			UserID:      user.UserID,
			Email:       user.Email,
			PhoneNumber: user.PhoneNumber,
			FullName:    user.FullName,
			IsActive:    true,
			IsVerified:  user.IsVerified,
			CreatedAt:   user.CreatedAt,
			UpdatedAt:   user.UpdatedAt,
			LastLogin:   user.LastLogin,
		},
	}, nil

}

func (s *AuthService) GetUserByEmail(email string) (*models.User, error) {
	return s.repo.GetUserByEmail(email)
}

func (s *AuthService) validatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("mật khẩu phải có ít nhất 8 ký tự")
	}

	var hasUpper, hasLower, hasDigit bool
	for _, c := range password {
		switch {
		case unicode.IsUpper(c):
			hasUpper = true
		case unicode.IsLower(c):
			hasLower = true
		case unicode.IsDigit(c):
			hasDigit = true
		}
	}

	if !hasUpper || !hasLower || !hasDigit {
		return errors.New("mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số")
	}

	return nil
}

func (s *AuthService) validateRole(role string) error {
	validRoles := []string{"admin", "member"}
	for _, validRole := range validRoles {
		if role == validRole {
			return nil
		}
	}
	return errors.New("vai trò không hợp lệ, chỉ chấp nhận 'admin' hoặc 'member'")
}

func (s *AuthService) mapRoleToDBName(role string) string {
	switch role {
	case "admin":
		return "Administrator"
	case "member":
		return "Member"
	default:
		return "Member"
	}
}

func (s *AuthService) validatePhoneNumber(phoneNumber string) error {
	if phoneNumber == "" {
		return nil
	}

	phoneRegex := `^(\+84|0)[0-9]{9,10}$`
	matched, err := regexp.MatchString(phoneRegex, phoneNumber)
	if err != nil {
		return errors.New("lỗi kiểm tra định dạng số điện thoại")
	}

	if !matched {
		return errors.New("số điện thoại không đúng định dạng (VD: 0901234567 hoặc +84901234567)")
	}

	return nil
}

func (s *AuthService) UpdateUser(user *models.User) error {
	if user == nil {
		return errors.New("thông tin user không hợp lệ")
	}
	return s.repo.UpdateUser(user)
}

func (s *AuthService) VerifyEmail(req *models.VerifyOTPRequest) error {
	user, err := s.repo.GetUserByEmail(req.Email)
	if err != nil {
		return errors.New("không tìm thấy tài khoản")
	}

	if user.IsVerified {
		return errors.New("tài khoản đã được xác thực trước đó")
	}

	userIDStr := strconv.Itoa(user.UserID)
	verified, _, err := utils.GetOTPStore().VerifyOTP(req.Email, req.OTP, &userIDStr)
	if err != nil {
		return err
	}
	if !verified {
		return errors.New("mã OTP không hợp lệ hoặc đã hết hạn")
	}

	user.IsVerified = true
	user.UpdatedAt = time.Now()

	if err := s.repo.UpdateUser(user); err != nil {
		return errors.New("không thể cập nhật trạng thái xác thực")
	}

	if s.homeService != nil {
		if err := s.ensureUserHasDefaultHome(user.UserID, user.Email); err != nil {
			log.Printf("Failed to create default home for user %d: %v", user.UserID, err)
		}
	}

	log.Printf("User %d email verified successfully", user.UserID)
	return nil
}

func (s *AuthService) ForgotPassword(email string) error {
	exists, err := s.repo.CheckEmailExists(email)
	if err != nil {
		return errors.New("lỗi hệ thống, vui lòng thử lại")
	}
	if !exists {
		return errors.New("không tìm thấy tài khoản với email này")
	}

	otp := utils.GetOTPStore().GenerateOTP(email)
	if err := utils.SendOTPEmail(email, otp); err != nil {
		log.Printf("Failed to send forgot password OTP: %v", err)
		return errors.New("không thể gửi mã OTP, vui lòng thử lại")
	}

	return nil
}

func (s *AuthService) ResetPassword(req *models.ResetPasswordRequest) error {
	if err := s.validatePassword(req.NewPassword); err != nil {
		return err
	}

	verified, _, err := utils.GetOTPStore().VerifyOTP(req.Email, req.OTP, nil)
	if err != nil {
		return err
	}
	if !verified {
		return errors.New("mã OTP không hợp lệ hoặc đã hết hạn")
	}

	user, err := s.repo.GetUserByEmail(req.Email)
	if err != nil {
		return errors.New("không tìm thấy tài khoản")
	}

	hash, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Failed to hash password: %v", err)
		return errors.New("lỗi hệ thống, vui lòng thử lại")
	}

	user.PasswordHash = string(hash)
	user.UpdatedAt = time.Now()

	if err := s.repo.UpdateUser(user); err != nil {
		log.Printf("Failed to update user password: %v", err)
		return errors.New("không thể cập nhật mật khẩu")
	}

	return nil
}

func (s *AuthService) ChangePassword(userID int, req *models.ChangePasswordRequest) error {
	if err := s.validatePassword(req.NewPassword); err != nil {
		return err
	}

	user, err := s.repo.GetUserByID(userID)
	if err != nil {
		return errors.New("không tìm thấy tài khoản")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.OldPassword)); err != nil {
		return errors.New("mật khẩu cũ không chính xác")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.NewPassword)); err == nil {
		return errors.New("mật khẩu mới phải khác mật khẩu cũ")
	}

	hash, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Printf("Failed to hash new password: %v", err)
		return errors.New("lỗi hệ thống, vui lòng thử lại")
	}

	user.PasswordHash = string(hash)
	user.UpdatedAt = time.Now()

	if err := s.repo.UpdateUser(user); err != nil {
		log.Printf("Failed to update user password: %v", err)
		return errors.New("không thể cập nhật mật khẩu")
	}

	return nil
}

func (s *AuthService) ensureUserHasDefaultHome(userID int, email string) error {
	homes, err := s.homeService.GetUserHomes(userID)
	if err != nil {
		return fmt.Errorf("không thể kiểm tra homes của user: %v", err)
	}

	if len(homes) > 0 {
		log.Printf("User %d already has %d home(s), skipping default home creation", userID, len(homes))
		return nil
	}

	defaultHomeName := fmt.Sprintf("Nhà của %s", email)
	defaultHomeAddress := "Địa chỉ mặc định"

	homeReq := &models.HomeRequest{
		Name:    defaultHomeName,
		Address: defaultHomeAddress,
	}

	homeID, err := s.homeService.CreateHome(userID, homeReq)
	if err != nil {
		return fmt.Errorf("không thể tạo home mặc định: %v", err)
	}

	log.Printf("Created default home (ID: %d) for user %d", homeID, userID)
	return nil
}
