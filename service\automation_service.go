package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/automation"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/device"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/permission"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"
)

type AutomationService struct {
	automationRepo automation.Repository
	deviceRepo     device.Repository
	deviceService  *DeviceService
	permissionRepo permission.Repository
}

func NewAutomationService(automationRepo automation.Repository, deviceRepo device.Repository, deviceService *DeviceService, permissionRepo permission.Repository) *AutomationService {
	return &AutomationService{
		automationRepo: automationRepo,
		deviceRepo:     deviceRepo,
		deviceService:  deviceService,
		permissionRepo: permissionRepo,
	}
}

func (s *AutomationService) CreateScheduleRule(userID int, name string, scheduleExpression string, actions []models.RuleActionRequest) (int, error) {
	if scheduleExpression == "" {
		return 0, errors.New("biểu thức lịch không được để trống")
	}

	rule := &models.AutomationRule{
		UserID:    userID,
		Name:      name,
		RuleType:  "schedule",
		IsActive:  true,
		CreatedAt: time.Now(),
	}

	ruleID, err := s.automationRepo.CreateRule(rule)
	if err != nil {
		return 0, err
	}

	schedule := &models.RuleSchedule{
		RuleID:             ruleID,
		ScheduleExpression: scheduleExpression,
		Timezone:           "UTC",
		NextRunAt:          s.calculateNextRun(scheduleExpression),
	}

	if err := s.automationRepo.CreateSchedule(schedule); err != nil {
		return 0, err
	}

	for _, actionReq := range actions {
		if !s.deviceRepo.DeviceExists(actionReq.DeviceID) {
			return 0, errors.New("thiết bị không tồn tại")
		}

		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, actionReq.DeviceID)
		if err != nil {
			return 0, fmt.Errorf("lỗi kiểm tra quyền thiết bị %d: %v", actionReq.DeviceID, err)
		}
		if !hasPermission {
			return 0, fmt.Errorf("không có quyền điều khiển thiết bị %d", actionReq.DeviceID)
		}

		if actionReq.Command != "" {
			device, err := s.deviceRepo.GetDeviceByID(actionReq.DeviceID)
			if err != nil {
				return 0, fmt.Errorf("không thể lấy thông tin thiết bị %d: %v", actionReq.DeviceID, err)
			}

			if device.DeviceTypeID != nil {
				deviceTypeName := s.getDeviceTypeName(*device.DeviceTypeID)
				if err := s.validateCommand(deviceTypeName, actionReq.Command); err != nil {
					return 0, fmt.Errorf("thiết bị %d: %v", actionReq.DeviceID, err)
				}
			}
		}

		propertyID := actionReq.PropertyID
		if propertyID == 0 && actionReq.Command != "" {
			templateID := s.getTemplateIDFromCommand(actionReq.Command)

			existingPropertyID, err := s.deviceRepo.GetPropertyIDByTemplate(actionReq.DeviceID, templateID)
			if err != nil {
				property := &models.DeviceProperty{
					DeviceID:   actionReq.DeviceID,
					TemplateID: templateID,
					Readable:   true,
					Writable:   true,
				}

				newPropertyID, createErr := s.deviceRepo.CreateProperty(property)
				if createErr != nil {
					return 0, fmt.Errorf("không thể tạo thuộc tính cho thiết bị %d: %v", actionReq.DeviceID, createErr)
				}
				propertyID = newPropertyID
			} else {
				propertyID = existingPropertyID
			}
		}

		if !s.deviceRepo.PropertyExists(propertyID) {
			return 0, fmt.Errorf("thuộc tính %d không tồn tại cho thiết bị %d", propertyID, actionReq.DeviceID)
		}

		action := &models.RuleAction{
			RuleID:     ruleID,
			DeviceID:   actionReq.DeviceID,
			PropertyID: propertyID,
			Value:      actionReq.Value,
		}

		if err := s.automationRepo.CreateAction(action); err != nil {
			return 0, err
		}
	}

	return ruleID, nil
}

func (s *AutomationService) getDeviceTypeName(deviceTypeID int) string {
	deviceTypeNames := map[int]string{
		1: "Smart Light",
		2: "Smart Switch",
		3: "Camera",
		4: "Temperature Sensor",
		5: "Smart AC",
	}

	if name, exists := deviceTypeNames[deviceTypeID]; exists {
		return name
	}
	return "Unknown Device"
}

func (s *AutomationService) validateCommand(deviceTypeName string, command string) error {
	validCommands := map[string][]string{
		"Smart Light":        {"turn_on", "turn_off", "toggle", "set_brightness"},
		"Smart Switch":       {"turn_on", "turn_off", "toggle"},
		"Camera":             {"turn_on", "turn_off"},
		"Temperature Sensor": {"get_temperature"},
		"Smart AC":           {"turn_on", "turn_off", "set_temperature"},
		"Unknown Device":     {"turn_on", "turn_off", "toggle"},
	}

	commands, exists := validCommands[deviceTypeName]
	if !exists {
		return fmt.Errorf("device type '%s' not supported", deviceTypeName)
	}

	for _, validCmd := range commands {
		if command == validCmd {
			return nil
		}
	}

	return fmt.Errorf("command '%s' not supported for device type '%s'", command, deviceTypeName)
}

func (s *AutomationService) getTemplateIDFromCommand(command string) int {
	commandToTemplateMap := map[string]int{
		"turn_on":         1,
		"turn_off":        1,
		"toggle":          1,
		"set_brightness":  2,
		"set_temperature": 3,
		"set_speed":       4,
		"set_volume":      2,
	}

	if templateID, exists := commandToTemplateMap[command]; exists {
		return templateID
	}
	return 1
}

func (s *AutomationService) CreateConditionRule(userID int, name string, conditions []models.RuleConditionRequest, actions []models.RuleActionRequest) (int, error) {
	if len(conditions) == 0 {
		return 0, errors.New("phải có ít nhất một điều kiện")
	}

	if len(actions) == 0 {
		return 0, errors.New("phải có ít nhất một hành động")
	}

	rule := &models.AutomationRule{
		UserID:    userID,
		Name:      name,
		RuleType:  "condition",
		IsActive:  true,
		CreatedAt: time.Now(),
	}

	ruleID, err := s.automationRepo.CreateRule(rule)
	if err != nil {
		return 0, err
	}

	for _, condReq := range conditions {
		if !s.deviceRepo.DeviceExists(condReq.DeviceID) {
			return 0, errors.New("thiết bị không tồn tại")
		}

		if condReq.PropertyID != nil && !s.deviceRepo.PropertyExists(*condReq.PropertyID) {
			return 0, errors.New("thuộc tính thiết bị không tồn tại")
		}

		condition := &models.RuleCondition{
			RuleID:          ruleID,
			DeviceID:        condReq.DeviceID,
			PropertyID:      condReq.PropertyID,
			Operator:        condReq.Operator,
			Value:           condReq.Value,
			LogicalOperator: condReq.LogicalOperator,
		}

		if err := s.automationRepo.CreateCondition(condition); err != nil {
			return 0, err
		}
	}

	for _, actionReq := range actions {
		if !s.deviceRepo.DeviceExists(actionReq.DeviceID) {
			return 0, errors.New("thiết bị không tồn tại")
		}

		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, actionReq.DeviceID)
		if err != nil {
			return 0, fmt.Errorf("lỗi kiểm tra quyền thiết bị %d: %v", actionReq.DeviceID, err)
		}
		if !hasPermission {
			return 0, fmt.Errorf("không có quyền điều khiển thiết bị %d", actionReq.DeviceID)
		}

		if actionReq.Command != "" {
			device, err := s.deviceRepo.GetDeviceByID(actionReq.DeviceID)
			if err != nil {
				return 0, fmt.Errorf("không thể lấy thông tin thiết bị %d: %v", actionReq.DeviceID, err)
			}

			if device.DeviceTypeID != nil {
				deviceTypeName := s.getDeviceTypeName(*device.DeviceTypeID)
				if err := s.validateCommand(deviceTypeName, actionReq.Command); err != nil {
					return 0, fmt.Errorf("thiết bị %d: %v", actionReq.DeviceID, err)
				}
			}
		}

		if !s.deviceRepo.PropertyExists(actionReq.PropertyID) {
			return 0, errors.New("thuộc tính thiết bị không tồn tại")
		}

		action := &models.RuleAction{
			RuleID:     ruleID,
			DeviceID:   actionReq.DeviceID,
			PropertyID: actionReq.PropertyID,
			Value:      actionReq.Value,
		}

		if err := s.automationRepo.CreateAction(action); err != nil {
			return 0, err
		}
	}

	return ruleID, nil
}

func (s *AutomationService) GetUserRules(userID int) ([]models.AutomationRule, error) {
	return s.automationRepo.GetRulesByUserID(userID)
}

func (s *AutomationService) UpdateRuleStatus(userID int, ruleID int, isActive bool) error {
	rule, err := s.automationRepo.GetRuleByID(ruleID)
	if err != nil {
		return errors.New("không tìm thấy quy tắc")
	}

	if rule.UserID != userID {
		return errors.New("không có quyền chỉnh sửa quy tắc này")
	}

	return s.automationRepo.UpdateRuleStatus(ruleID, isActive)
}

func (s *AutomationService) DeleteRule(userID int, ruleID int) error {
	rule, err := s.automationRepo.GetRuleByID(ruleID)
	if err != nil {
		return errors.New("không tìm thấy quy tắc")
	}

	if rule.UserID != userID {
		return errors.New("không có quyền xóa quy tắc này")
	}

	return s.automationRepo.DeleteRule(ruleID)
}

func (s *AutomationService) ExecuteRule(ruleID int) error {
	log.Printf("🤖 EXECUTING AUTOMATION RULE %d", ruleID)

	rule, err := s.automationRepo.GetRuleByID(ruleID)
	if err != nil {
		log.Printf("❌ Failed to get rule %d: %v", ruleID, err)
		return err
	}

	log.Printf("📋 Rule details: ID=%d, Name=%s, Active=%t, UserID=%d", rule.RuleID, rule.Name, rule.IsActive, rule.UserID)

	if !rule.IsActive {
		log.Printf("❌ Rule %d is not active", ruleID)
		return errors.New("quy tắc không hoạt động")
	}

	actions, err := s.automationRepo.GetActionsByRuleID(ruleID)
	if err != nil {
		log.Printf("❌ Failed to get actions for rule %d: %v", ruleID, err)
		return err
	}

	log.Printf("⚡ Found %d actions to execute for rule %d", len(actions), ruleID)

	for i, action := range actions {
		log.Printf("🎯 Executing action %d/%d: DeviceID=%d, PropertyID=%d, Value=%s", i+1, len(actions), action.DeviceID, action.PropertyID, action.Value)

		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(rule.UserID, action.DeviceID)
		if err != nil {
			log.Printf("❌ Error checking permission for device %d in rule %d: %v", action.DeviceID, ruleID, err)
			continue
		}
		if !hasPermission {
			log.Printf("❌ Rule owner %d does not have permission to control device %d, skipping action", rule.UserID, action.DeviceID)
			continue
		}

		log.Printf("✅ Permission check passed for device %d", action.DeviceID)

		command := s.determineCommandByPropertyID(action.PropertyID, action.Value)
		log.Printf("📤 Sending command: %s (value: %s) to device %d", command, action.Value, action.DeviceID)

		if err := s.deviceService.ControlDevice(0, action.DeviceID, command, action.Value); err != nil {
			log.Printf("❌ Error executing command for device %d in rule %d: %v", action.DeviceID, ruleID, err)
			continue
		}

		log.Printf("✅ Successfully sent command %s to device %d", command, action.DeviceID)
	}

	return nil
}

func (s *AutomationService) CheckConditions() error {
	rules, err := s.automationRepo.GetActiveConditionRules()
	if err != nil {
		return err
	}

	for _, rule := range rules {
		shouldExecute, err := s.evaluateRuleConditions(rule.RuleID)
		if err != nil {
			continue
		}

		if shouldExecute {
			s.ExecuteRule(rule.RuleID)
		}
	}

	return nil
}

func (s *AutomationService) evaluateRuleConditions(ruleID int) (bool, error) {
	conditions, err := s.automationRepo.GetConditionsByRuleID(ruleID)
	if err != nil {
		return false, err
	}

	if len(conditions) == 0 {
		return false, nil
	}

	results := make([]bool, len(conditions))
	for i, condition := range conditions {
		// Always use device status column for sensor data
		device, err := s.deviceRepo.GetDeviceByID(condition.DeviceID)
		if err != nil {
			log.Printf("Error getting device %d: %v", condition.DeviceID, err)
			return false, err
		}

		currentValue := device.Status
		log.Printf("Automation condition evaluation: Device %d, Status: %s, Operator: %s, Target: %s",
			condition.DeviceID, currentValue, condition.Operator, condition.Value)

		results[i] = s.compareValues(currentValue, condition.Operator, condition.Value)
		log.Printf("Condition result: %t", results[i])
	}

	return s.combineResults(results, conditions), nil
}

func (s *AutomationService) compareValues(currentValue, operator, targetValue string) bool {
	switch operator {
	case "=":
		return currentValue == targetValue
	case "!=":
		return currentValue != targetValue
	case ">":
		return currentValue > targetValue
	case "<":
		return currentValue < targetValue
	case ">=":
		return currentValue >= targetValue
	case "<=":
		return currentValue <= targetValue
	default:
		return false
	}
}

func (s *AutomationService) combineResults(results []bool, conditions []models.RuleCondition) bool {
	if len(results) == 0 {
		return false
	}

	if len(results) == 1 {
		return results[0]
	}

	finalResult := results[0]
	for i := 1; i < len(results); i++ {
		if conditions[i].LogicalOperator == "OR" {
			finalResult = finalResult || results[i]
		} else {
			finalResult = finalResult && results[i]
		}
	}

	return finalResult
}

func (s *AutomationService) calculateNextRun(cronExpression string) *time.Time {
	parts := strings.Fields(cronExpression)
	if len(parts) != 5 {
		nextRun := time.Now().Add(time.Hour)
		return &nextRun
	}

	now := time.Now()

	minute, err1 := strconv.Atoi(parts[0])
	hour, err2 := strconv.Atoi(parts[1])

	if err1 != nil || err2 != nil || minute < 0 || minute > 59 || hour < 0 || hour > 23 {
		nextRun := time.Now().Add(time.Hour)
		return &nextRun
	}

	var nextRun time.Time

	today := time.Date(now.Year(), now.Month(), now.Day(), hour, minute, 0, 0, now.Location())

	if today.After(now) {
		nextRun = today
	} else {
		nextRun = today.AddDate(0, 0, 1)
	}

	log.Printf("🕐 Calculated next run time: %s for cron '%s' (current: %s)", nextRun.Format("2006-01-02 15:04:05"), cronExpression, now.Format("2006-01-02 15:04:05"))

	return &nextRun
}

func (s *AutomationService) convertScheduleToCron(time string, days []string) (string, error) {
	timeParts := strings.Split(time, ":")
	if len(timeParts) != 2 {
		return "", fmt.Errorf("định dạng thời gian không hợp lệ, cần HH:MM")
	}

	hour, err := strconv.Atoi(timeParts[0])
	if err != nil || hour < 0 || hour > 23 {
		return "", fmt.Errorf("giờ không hợp lệ: %s", timeParts[0])
	}

	minute, err := strconv.Atoi(timeParts[1])
	if err != nil || minute < 0 || minute > 59 {
		return "", fmt.Errorf("phút không hợp lệ: %s", timeParts[1])
	}

	dayMap := map[string]string{
		"sunday":    "0",
		"monday":    "1",
		"tuesday":   "2",
		"wednesday": "3",
		"thursday":  "4",
		"friday":    "5",
		"saturday":  "6",
	}

	var cronDays []string
	for _, day := range days {
		if cronDay, exists := dayMap[strings.ToLower(day)]; exists {
			cronDays = append(cronDays, cronDay)
		} else {
			return "", fmt.Errorf("ngày không hợp lệ: %s", day)
		}
	}

	if len(cronDays) == 0 {
		return "", fmt.Errorf("cần ít nhất một ngày trong tuần")
	}

	cronExpression := fmt.Sprintf("%d %d * * %s", minute, hour, strings.Join(cronDays, ","))
	return cronExpression, nil
}

func (s *AutomationService) CreateAutomationRule(userID int, req *models.CreateAutomationRuleRequest) (int, error) {
	if req.Name == "" {
		return 0, errors.New("tên quy tắc không được để trống")
	}

	validRuleTypes := []string{"schedule", "condition", "sensor"}
	isValidRuleType := false
	for _, validType := range validRuleTypes {
		if req.RuleType == validType {
			isValidRuleType = true
			break
		}
	}

	if !isValidRuleType {
		return 0, fmt.Errorf("loại quy tắc '%s' không hợp lệ. Các loại được hỗ trợ: %s",
			req.RuleType, strings.Join(validRuleTypes, ", "))
	}

	if len(req.Actions) == 0 {
		return 0, errors.New("phải có ít nhất một hành động")
	}

	if req.RuleType == "schedule" {
		if req.Schedule == nil {
			return 0, errors.New("quy tắc lịch phải có thông tin lịch trình (schedule)")
		}

		if req.Schedule.ScheduleExpression == "" && (req.Schedule.Time == "" || len(req.Schedule.Days) == 0) {
			return 0, errors.New("quy tắc lịch phải có biểu thức thời gian (schedule_expression) hoặc thời gian và ngày (time + days)")
		}
	}

	if req.RuleType == "condition" {
		if len(req.Conditions) == 0 {
			return 0, errors.New("quy tắc điều kiện phải có ít nhất một điều kiện")
		}
	}

	if req.RuleType == "sensor" {
		if len(req.Conditions) == 0 {
			return 0, errors.New("quy tắc cảm biến phải có ít nhất một điều kiện cảm biến")
		}
	}

	fmt.Printf("DEBUG: Request HomeID: %d\n", req.HomeID)
	fmt.Printf("DEBUG: HomeID pointer: %v\n", &req.HomeID)

	rule := &models.AutomationRule{
		UserID:      userID,
		HomeID:      &req.HomeID,
		Name:        req.Name,
		Description: req.Description,
		RuleType:    req.RuleType,
		IsActive:    req.IsActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	fmt.Printf("DEBUG: Rule HomeID before save: %v\n", rule.HomeID)

	ruleID, err := s.automationRepo.CreateRule(rule)
	if err != nil {
		return 0, err
	}

	fmt.Printf("DEBUG: Created rule ID: %d\n", ruleID)

	if req.RuleType == "schedule" && req.Schedule != nil {
		var cronExpr string

		if req.Schedule.ScheduleExpression != "" {
			cronExpr = req.Schedule.ScheduleExpression
		} else if req.Schedule.Time != "" && len(req.Schedule.Days) > 0 {
			var err error
			cronExpr, err = s.convertScheduleToCron(req.Schedule.Time, req.Schedule.Days)
			if err != nil {
				return 0, fmt.Errorf("lỗi chuyển đổi lịch trình: %v", err)
			}
		} else {
			return 0, errors.New("thiếu thông tin lịch trình")
		}

		cronParts := strings.Fields(cronExpr)
		if len(cronParts) != 5 && len(cronParts) != 6 {
			return 0, fmt.Errorf("biểu thức cron '%s' không hợp lệ. Định dạng chuẩn: 'phút giờ ngày tháng thứ' (5 phần) hoặc 'giây phút giờ ngày tháng thứ' (6 phần). Hiện tại có %d phần",
				cronExpr, len(cronParts))
		}

		timezone := req.Schedule.Timezone
		if timezone == "" {
			timezone = "UTC"
		}

		validTimezones := []string{"UTC", "Asia/Ho_Chi_Minh", "America/New_York", "Europe/London", "Asia/Tokyo"}
		isValidTimezone := false
		for _, validTz := range validTimezones {
			if timezone == validTz {
				isValidTimezone = true
				break
			}
		}
		if !isValidTimezone {
			log.Printf("Warning: timezone '%s' may not be supported. Recommended: %s", timezone, strings.Join(validTimezones, ", "))
		}

		schedule := &models.RuleSchedule{
			RuleID:             ruleID,
			ScheduleExpression: cronExpr,
			Timezone:           timezone,
			NextRunAt:          s.calculateNextRun(cronExpr),
		}

		if err := s.automationRepo.CreateSchedule(schedule); err != nil {
			return 0, fmt.Errorf("lỗi tạo lịch trình: %v", err)
		}
	}

	if req.RuleType == "condition" || req.RuleType == "sensor" {
		for i, condReq := range req.Conditions {
			if !s.deviceRepo.DeviceExists(condReq.DeviceID) {
				return 0, fmt.Errorf("điều kiện thứ %d: thiết bị với ID %d không tồn tại", i+1, condReq.DeviceID)
			}

			if condReq.PropertyID != nil && !s.deviceRepo.PropertyExists(*condReq.PropertyID) {
				return 0, fmt.Errorf("điều kiện thứ %d: thuộc tính với ID %d không tồn tại", i+1, *condReq.PropertyID)
			}

			validOperators := []string{"=", "!=", ">", "<", ">=", "<=", "contains", "not_contains"}
			isValidOperator := false
			for _, validOp := range validOperators {
				if condReq.Operator == validOp {
					isValidOperator = true
					break
				}
			}
			if !isValidOperator {
				return 0, fmt.Errorf("điều kiện thứ %d: toán tử '%s' không hợp lệ. Các toán tử được hỗ trợ: %s",
					i+1, condReq.Operator, strings.Join(validOperators, ", "))
			}

			if condReq.Value == "" {
				return 0, fmt.Errorf("điều kiện thứ %d: giá trị không được để trống", i+1)
			}

			condition := &models.RuleCondition{
				RuleID:          ruleID,
				DeviceID:        condReq.DeviceID,
				PropertyID:      condReq.PropertyID,
				Operator:        condReq.Operator,
				Value:           condReq.Value,
				LogicalOperator: condReq.LogicalOperator,
			}

			if err := s.automationRepo.CreateCondition(condition); err != nil {
				return 0, fmt.Errorf("lỗi tạo điều kiện thứ %d: %v", i+1, err)
			}
		}
	}

	for i, actionReq := range req.Actions {
		log.Printf("Validating action %d: DeviceID=%d, PropertyID=%d", i+1, actionReq.DeviceID, actionReq.PropertyID)

		if !s.deviceRepo.DeviceExists(actionReq.DeviceID) {
			log.Printf("Device %d does not exist for action %d", actionReq.DeviceID, i+1)
			return 0, fmt.Errorf("hành động thứ %d: thiết bị với ID %d không tồn tại", i+1, actionReq.DeviceID)
		}

		log.Printf("Checking device control permission for user %d on device %d", userID, actionReq.DeviceID)
		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, actionReq.DeviceID)
		if err != nil {
			log.Printf("Error checking permission for device %d: %v", actionReq.DeviceID, err)
			return 0, fmt.Errorf("hành động thứ %d: lỗi kiểm tra quyền thiết bị %d: %v", i+1, actionReq.DeviceID, err)
		}
		if !hasPermission {
			log.Printf("User %d does not have control permission for device %d", userID, actionReq.DeviceID)
			return 0, fmt.Errorf("hành động thứ %d: không có quyền điều khiển thiết bị %d", i+1, actionReq.DeviceID)
		}
		log.Printf("Permission check passed for user %d on device %d", userID, actionReq.DeviceID)

		if actionReq.Command != "" {
			device, err := s.deviceRepo.GetDeviceByID(actionReq.DeviceID)
			if err != nil {
				log.Printf("Error getting device %d info: %v", actionReq.DeviceID, err)
				return 0, fmt.Errorf("hành động thứ %d: không thể lấy thông tin thiết bị %d: %v", i+1, actionReq.DeviceID, err)
			}

			if device.DeviceTypeID != nil {
				deviceTypeName := s.getDeviceTypeName(*device.DeviceTypeID)
				log.Printf("Validating command '%s' for device type '%s'", actionReq.Command, deviceTypeName)
				if err := s.validateCommand(deviceTypeName, actionReq.Command); err != nil {
					log.Printf("Command validation failed for device %d: %v", actionReq.DeviceID, err)
					return 0, fmt.Errorf("hành động thứ %d: %v", i+1, err)
				}
				log.Printf("Command validation passed for device %d", actionReq.DeviceID)
			} else {
				log.Printf("Warning: Device %d has no device type, skipping command validation", actionReq.DeviceID)
			}
		}

		propertyID := actionReq.PropertyID
		if propertyID == 0 || !s.deviceRepo.PropertyExists(propertyID) {
			if actionReq.Command != "" {
				templateID := s.getTemplateIDFromCommand(actionReq.Command)
				log.Printf("Auto-resolving property for device %d with command '%s' -> template %d", actionReq.DeviceID, actionReq.Command, templateID)

				existingPropertyID, err := s.deviceRepo.GetPropertyIDByTemplate(actionReq.DeviceID, templateID)
				if err != nil {
					log.Printf("Property not found for device %d template %d, creating new property", actionReq.DeviceID, templateID)
					property := &models.DeviceProperty{
						DeviceID:   actionReq.DeviceID,
						TemplateID: templateID,
						Readable:   true,
						Writable:   true,
					}

					newPropertyID, createErr := s.deviceRepo.CreateProperty(property)
					if createErr != nil {
						return 0, fmt.Errorf("hành động thứ %d: không thể tạo thuộc tính cho thiết bị %d: %v", i+1, actionReq.DeviceID, createErr)
					}
					propertyID = newPropertyID
					log.Printf("Created new property %d for device %d", propertyID, actionReq.DeviceID)
				} else {
					propertyID = existingPropertyID
					log.Printf("Found existing property %d for device %d", propertyID, actionReq.DeviceID)
				}
			} else {
				return 0, fmt.Errorf("hành động thứ %d: không thể xác định thuộc tính thiết bị (thiếu command hoặc property_id)", i+1)
			}
		}

		if !s.deviceRepo.PropertyExists(propertyID) {
			return 0, fmt.Errorf("hành động thứ %d: thuộc tính với ID %d không tồn tại", i+1, propertyID)
		}

		value := actionReq.Value
		if value == "" {
			switch actionReq.Command {
			case "turn_on":
				value = "on"
			case "turn_off":
				value = "off"
			case "toggle":
				value = "on"
			default:
				return 0, fmt.Errorf("hành động thứ %d: giá trị không được để trống cho lệnh '%s'", i+1, actionReq.Command)
			}
		}

		if actionReq.DelaySeconds < 0 {
			return 0, fmt.Errorf("hành động thứ %d: thời gian trễ không được âm (hiện tại: %d giây)", i+1, actionReq.DelaySeconds)
		}

		log.Printf("Validation passed for action %d: DeviceID=%d, PropertyID=%d", i+1, actionReq.DeviceID, propertyID)

		action := &models.RuleAction{
			RuleID:       ruleID,
			DeviceID:     actionReq.DeviceID,
			PropertyID:   propertyID,
			Value:        value,
			DelaySeconds: actionReq.DelaySeconds,
		}

		if err := s.automationRepo.CreateAction(action); err != nil {
			return 0, fmt.Errorf("lỗi tạo hành động thứ %d: %v", i+1, err)
		}
	}

	return ruleID, nil
}

func (s *AutomationService) UpdateAutomationRule(userID int, ruleID int, req *models.UpdateAutomationRuleRequest) error {
	rule, err := s.automationRepo.GetRuleByID(ruleID)
	if err != nil {
		return errors.New("không tìm thấy quy tắc")
	}

	if rule.UserID != userID {
		return errors.New("không có quyền chỉnh sửa quy tắc này")
	}

	if req.Name != "" {
		rule.Name = req.Name
	}
	if req.Description != "" {
		rule.Description = req.Description
	}
	if req.IsActive != nil {
		rule.IsActive = *req.IsActive
	}
	rule.UpdatedAt = time.Now()

	if err := s.automationRepo.UpdateRule(rule); err != nil {
		return err
	}

	if len(req.Conditions) > 0 {
		if err := s.automationRepo.DeleteConditionsByRuleID(ruleID); err != nil {
			return err
		}

		for _, condReq := range req.Conditions {
			condition := &models.RuleCondition{
				RuleID:          ruleID,
				DeviceID:        condReq.DeviceID,
				PropertyID:      condReq.PropertyID,
				Operator:        condReq.Operator,
				Value:           condReq.Value,
				LogicalOperator: condReq.LogicalOperator,
			}

			if err := s.automationRepo.CreateCondition(condition); err != nil {
				return err
			}
		}
	}

	if len(req.Actions) > 0 {
		if err := s.automationRepo.DeleteActionsByRuleID(ruleID); err != nil {
			return err
		}

		for _, actionReq := range req.Actions {
			action := &models.RuleAction{
				RuleID:       ruleID,
				DeviceID:     actionReq.DeviceID,
				PropertyID:   actionReq.PropertyID,
				Value:        actionReq.Value,
				DelaySeconds: actionReq.DelaySeconds,
			}

			if err := s.automationRepo.CreateAction(action); err != nil {
				return err
			}
		}
	}

	if req.Schedule != nil {
		schedule, err := s.automationRepo.GetScheduleByRuleID(ruleID)
		if err != nil {
			schedule = &models.RuleSchedule{
				RuleID:             ruleID,
				ScheduleExpression: req.Schedule.ScheduleExpression,
				Timezone:           req.Schedule.Timezone,
				NextRunAt:          s.calculateNextRun(req.Schedule.ScheduleExpression),
			}
			return s.automationRepo.CreateSchedule(schedule)
		} else {
			schedule.ScheduleExpression = req.Schedule.ScheduleExpression
			if req.Schedule.Timezone != "" {
				schedule.Timezone = req.Schedule.Timezone
			}
			schedule.NextRunAt = s.calculateNextRun(req.Schedule.ScheduleExpression)
			return s.automationRepo.UpdateSchedule(schedule)
		}
	}

	return nil
}

func (s *AutomationService) GetRuleTemplates() ([]models.RuleTemplateResponse, error) {
	templates, err := s.automationRepo.GetRuleTemplates()
	if err != nil {
		return nil, err
	}

	var result []models.RuleTemplateResponse
	for _, template := range templates {
		result = append(result, models.RuleTemplateResponse{
			TemplateID:       template.TemplateID,
			Name:             template.Name,
			Description:      template.Description,
			RuleType:         template.RuleType,
			IsSystemTemplate: template.IsSystemTemplate,
			TemplateData:     template.TemplateData,
			CreatedAt:        template.CreatedAt,
		})
	}

	return result, nil
}

func (s *AutomationService) GetExecutionHistory(userID int, ruleID int, limit int) ([]models.RuleExecutionResponse, error) {
	var logs []models.RuleExecutionLog
	var err error

	if ruleID > 0 {
		rule, err := s.automationRepo.GetRuleByID(ruleID)
		if err != nil {
			return nil, errors.New("không tìm thấy quy tắc")
		}

		if rule.UserID != userID {
			return nil, errors.New("không có quyền xem lịch sử quy tắc này")
		}

		logs, err = s.automationRepo.GetExecutionHistory(ruleID, limit)
	} else {
		logs, err = s.automationRepo.GetExecutionHistoryByUserID(userID, limit)
	}

	if err != nil {
		return nil, err
	}

	var result []models.RuleExecutionResponse
	for _, log := range logs {
		ruleName := ""
		if log.Rule != nil {
			ruleName = log.Rule.Name
		}

		result = append(result, models.RuleExecutionResponse{
			LogID:           log.LogID,
			RuleID:          log.RuleID,
			RuleName:        ruleName,
			Status:          log.Status,
			Message:         log.Message,
			ExecutionTimeMs: log.ExecutionTimeMs,
			TriggeredBy:     log.TriggeredBy,
			CreatedAt:       log.CreatedAt,
		})
	}

	return result, nil
}

func (s *AutomationService) GetRuleByID(ruleID int) (*models.AutomationRule, error) {
	return s.automationRepo.GetRuleByID(ruleID)
}

func (s *AutomationService) determineCommand(propertyName, value string) string {
	switch propertyName {
	case "power", "status":
		if value == "true" || value == "1" || value == "on" {
			return "turn_on"
		} else {
			return "turn_off"
		}
	case "brightness":
		return "set_brightness"
	case "temperature":
		return "set_temperature"
	case "speed":
		return "set_speed"
	default:
		return "toggle"
	}
}

func (s *AutomationService) determineCommandByPropertyID(propertyID int, value string) string {
	property, err := s.deviceRepo.GetPropertyByID(propertyID)
	if err != nil {
		log.Printf("Warning: Could not get property %d, using value-based logic", propertyID)
	} else {
		switch property.TemplateID {
		case 1:
			if value == "true" || value == "1" || value == "on" {
				return "turn_on"
			} else if value == "false" || value == "0" || value == "off" {
				return "turn_off"
			} else {
				return "toggle"
			}
		case 2:
			return "set_brightness"
		case 3:
			return "set_temperature"
		}
	}

	if value == "true" || value == "1" || value == "on" {
		return "turn_on"
	} else if value == "false" || value == "0" || value == "off" {
		return "turn_off"
	} else {
		return "toggle"
	}
}

func (s *AutomationService) GetHomeRules(userID int, homeID int) ([]models.AutomationRuleResponse, error) {

	rules, err := s.automationRepo.GetRulesByHomeID(homeID)
	if err != nil {
		return nil, err
	}

	var result []models.AutomationRuleResponse
	for _, rule := range rules {
		if rule.UserID == userID {
			ruleResponse := models.AutomationRuleResponse{
				RuleID:      rule.RuleID,
				Name:        rule.Name,
				Description: rule.Description,
				RuleType:    rule.RuleType,
				IsActive:    rule.IsActive,
				CreatedAt:   rule.CreatedAt,
				UpdatedAt:   rule.UpdatedAt,
				Conditions:  rule.Conditions,
				Actions:     rule.Actions,
				Schedule:    rule.Schedule,
				Template:    rule.Template,
			}
			result = append(result, ruleResponse)
		}
	}

	return result, nil
}

func (s *AutomationService) GetHomeRule(userID int, homeID int, ruleID int) (*models.AutomationRuleResponse, error) {
	rule, err := s.automationRepo.GetRuleByID(ruleID)
	if err != nil {
		return nil, fmt.Errorf("không tìm thấy quy tắc với ID %d", ruleID)
	}

	if rule.HomeID == nil {
		return nil, fmt.Errorf("quy tắc ID %d không thuộc về nhà nào (home_id = NULL)", ruleID)
	}

	if *rule.HomeID != homeID {
		return nil, fmt.Errorf("quy tắc ID %d thuộc về nhà %d, không phải nhà %d", ruleID, *rule.HomeID, homeID)
	}

	if rule.UserID != userID {
		return nil, fmt.Errorf("quy tắc ID %d thuộc về user %d, bạn không có quyền truy cập (user %d)", ruleID, rule.UserID, userID)
	}

	response := &models.AutomationRuleResponse{
		RuleID:      rule.RuleID,
		Name:        rule.Name,
		Description: rule.Description,
		RuleType:    rule.RuleType,
		IsActive:    rule.IsActive,
		CreatedAt:   rule.CreatedAt,
		UpdatedAt:   rule.UpdatedAt,
		Conditions:  rule.Conditions,
		Actions:     rule.Actions,
		Schedule:    rule.Schedule,
		Template:    rule.Template,
	}

	return response, nil
}

func (s *AutomationService) ExecuteHomeRule(userID int, homeID int, ruleID int) (*models.RuleExecutionResponse, error) {
	rule, err := s.automationRepo.GetRuleByID(ruleID)
	if err != nil {
		return nil, errors.New("không tìm thấy quy tắc")
	}

	if rule.HomeID == nil || *rule.HomeID != homeID {
		return nil, errors.New("quy tắc không thuộc về nhà này")
	}

	if rule.UserID != userID {
		return nil, errors.New("không có quyền thực thi quy tắc này")
	}

	if !rule.IsActive {
		return nil, errors.New("quy tắc đã bị tắt")
	}

	startTime := time.Now()

	actions, err := s.automationRepo.GetActionsByRuleID(ruleID)
	if err != nil {
		return nil, fmt.Errorf("không thể lấy actions của rule: %v", err)
	}

	if len(actions) == 0 {
		return nil, errors.New("rule không có actions để thực thi")
	}

	var executionErrors []string
	successCount := 0

	for _, action := range actions {
		if !s.deviceRepo.DeviceExists(action.DeviceID) {
			executionErrors = append(executionErrors, fmt.Sprintf("Device %d không tồn tại", action.DeviceID))
			continue
		}

		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, action.DeviceID)
		if err != nil {
			executionErrors = append(executionErrors, fmt.Sprintf("Device %d: Lỗi kiểm tra quyền - %v", action.DeviceID, err))
			continue
		}
		if !hasPermission {
			executionErrors = append(executionErrors, fmt.Sprintf("Device %d: Không có quyền điều khiển thiết bị", action.DeviceID))
			continue
		}

		command := s.determineCommandByPropertyID(action.PropertyID, action.Value)

		err = s.executeDeviceCommand(action.DeviceID, command, action.Value)
		if err != nil {
			executionErrors = append(executionErrors, fmt.Sprintf("Device %d: %v", action.DeviceID, err))
		} else {
			successCount++
		}

		if action.DelaySeconds > 0 {
			time.Sleep(time.Duration(action.DelaySeconds) * time.Second)
		}
	}

	executionTime := time.Since(startTime)
	status := "success"
	message := fmt.Sprintf("Thực thi thành công %d/%d actions", successCount, len(actions))

	if len(executionErrors) > 0 {
		if successCount == 0 {
			status = "failed"
			message = "Thực thi thất bại: " + strings.Join(executionErrors, "; ")
		} else {
			status = "partial"
			message = fmt.Sprintf("Thực thi một phần (%d/%d thành công): %s", successCount, len(actions), strings.Join(executionErrors, "; "))
		}
	}

	executionLog := &models.RuleExecutionLog{
		RuleID:          ruleID,
		Status:          status,
		Message:         message,
		ExecutionTimeMs: int(executionTime.Milliseconds()),
		TriggeredBy:     "manual",
		CreatedAt:       time.Now(),
	}

	err = s.automationRepo.CreateExecutionLog(executionLog)
	if err != nil {
		log.Printf("Failed to create execution log: %v", err)
	}

	response := &models.RuleExecutionResponse{
		LogID:           executionLog.LogID,
		RuleID:          ruleID,
		RuleName:        rule.Name,
		Status:          status,
		Message:         message,
		ExecutionTimeMs: int(executionTime.Milliseconds()),
		TriggeredBy:     "manual",
		CreatedAt:       time.Now(),
	}

	return response, nil
}

func (s *AutomationService) executeDeviceCommand(deviceID int, command string, value string) error {

	log.Printf("Executing command on device %d: %s = %s", deviceID, command, value)

	return nil
}
