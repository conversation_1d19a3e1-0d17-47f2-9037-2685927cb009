package user

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
)

type Repository interface {
	CreateUser(user *models.User) error
	GetUserByID(userID int) (*models.User, error)
	GetUserByUsername(username string) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	UpdateUser(user *models.User) error
	DeleteUser(userID int) error

	CheckUsernameExists(username string) (bool, error)
	CheckEmailExists(email string) (bool, error)
	CheckPhoneExists(phoneNumber string) (bool, error)

	IsUserAdmin(userID int) (bool, error)
	AssignRole(userID int, roleName string) error
	GetUserRoles(userID int) ([]models.Role, error)

	UpdateDeviceState(state *models.DeviceState) error
}
