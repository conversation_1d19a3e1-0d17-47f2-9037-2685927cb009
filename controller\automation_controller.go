package controller

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"

	"github.com/gin-gonic/gin"
)

type AutomationController struct {
	automationService *service.AutomationService
}

func NewAutomationController(automationService *service.AutomationService) *AutomationController {
	return &AutomationController{
		automationService: automationService,
	}
}

func (ac *AutomationController) CreateScheduleRule(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		HomeID      int    `json:"home_id" binding:"required"`
		RuleType    string `json:"rule_type" binding:"required"`
		IsActive    bool   `json:"is_active"`
		Actions     []struct {
			DeviceID int    `json:"device_id" binding:"required"`
			Command  string `json:"command" binding:"required"`
			Value    string `json:"value"`
			Delay    int    `json:"delay"`
		} `json:"actions" binding:"required,dive"`
		Schedule struct {
			Time     string   `json:"time" binding:"required"`
			Days     []string `json:"days" binding:"required"`
			Timezone string   `json:"timezone" binding:"required"`
			Repeat   string   `json:"repeat" binding:"required"`
		} `json:"schedule" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	cronExpression, err := ac.convertScheduleToCron(req.Schedule.Time, req.Schedule.Days)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Lịch trình không hợp lệ: "+err.Error())
		return
	}

	var actions []models.RuleActionRequest
	for _, action := range req.Actions {
		var value string
		switch action.Command {
		case "turn_on":
			value = "true"
		case "turn_off":
			value = "false"
		case "toggle":
			value = "toggle"
		default:
			value = action.Value
		}

		propertyID := ac.getPropertyIDFromCommand(action.Command)

		actions = append(actions, models.RuleActionRequest{
			DeviceID:   action.DeviceID,
			PropertyID: propertyID,
			Value:      value,
			Command:    action.Command,
		})
	}

	userIDInt := int(userID.(uint))

	automationReq := &models.CreateAutomationRuleRequest{
		Name:        req.Name,
		Description: req.Description,
		RuleType:    "schedule",
		HomeID:      req.HomeID,
		IsActive:    req.IsActive,
		Actions:     actions,
		Schedule: &models.RuleScheduleRequest{
			ScheduleExpression: cronExpression,
			Timezone:           req.Schedule.Timezone,
		},
	}

	ruleID, err := ac.automationService.CreateAutomationRule(userIDInt, automationReq)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Tạo kịch bản tự động thành công", gin.H{"rule_id": ruleID})
}

func (ac *AutomationController) convertScheduleToCron(time string, days []string) (string, error) {
	timeParts := strings.Split(time, ":")
	if len(timeParts) != 2 {
		return "", fmt.Errorf("định dạng thời gian không hợp lệ, cần HH:MM")
	}

	hour, err := strconv.Atoi(timeParts[0])
	if err != nil || hour < 0 || hour > 23 {
		return "", fmt.Errorf("giờ không hợp lệ: %s", timeParts[0])
	}

	minute, err := strconv.Atoi(timeParts[1])
	if err != nil || minute < 0 || minute > 59 {
		return "", fmt.Errorf("phút không hợp lệ: %s", timeParts[1])
	}

	dayMap := map[string]string{
		"sunday":    "0",
		"monday":    "1",
		"tuesday":   "2",
		"wednesday": "3",
		"thursday":  "4",
		"friday":    "5",
		"saturday":  "6",
	}

	var cronDays []string
	for _, day := range days {
		if cronDay, exists := dayMap[strings.ToLower(day)]; exists {
			cronDays = append(cronDays, cronDay)
		} else {
			return "", fmt.Errorf("ngày không hợp lệ: %s", day)
		}
	}

	if len(cronDays) == 0 {
		return "", fmt.Errorf("cần ít nhất một ngày trong tuần")
	}

	cronExpression := fmt.Sprintf("%d %d * * %s", minute, hour, strings.Join(cronDays, ","))
	return cronExpression, nil
}

func (ac *AutomationController) getPropertyIDFromCommand(command string) int {
	commandToPropertyMap := map[string]int{
		"turn_on":         1,
		"turn_off":        1,
		"toggle":          1,
		"set_brightness":  2,
		"set_temperature": 3,
		"set_speed":       4,
		"set_volume":      5,
	}

	if propertyID, exists := commandToPropertyMap[command]; exists {
		return propertyID
	}
	return 1
}

func (ac *AutomationController) CreateConditionRule(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.CreateConditionRuleRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	var conditions []models.RuleConditionRequest
	for _, condition := range req.Conditions {
		var propertyID *int
		if condition.PropertyName != "" {
			propertyID = nil
		}

		conditions = append(conditions, models.RuleConditionRequest{
			DeviceID:   condition.DeviceID,
			PropertyID: propertyID,
			Operator:   condition.Operator,
			Value:      condition.Value,
		})
	}

	var actions []models.RuleActionRequest
	for _, action := range req.Actions {
		var value string
		if action.Value != "" {
			value = action.Value
		} else {
			switch action.Command {
			case "turn_on":
				value = "on"
			case "turn_off":
				value = "off"
			case "toggle":
				value = "on"
			default:
				value = action.Value
			}
		}

		propertyID := ac.getPropertyIDFromCommand(action.Command)

		actions = append(actions, models.RuleActionRequest{
			DeviceID:   action.DeviceID,
			PropertyID: propertyID,
			Value:      value,
			Command:    action.Command,
		})
	}

	userIDInt := int(userID.(uint))

	automationReq := &models.CreateAutomationRuleRequest{
		Name:        req.Name,
		Description: req.Description,
		RuleType:    "condition",
		HomeID:      req.HomeID,
		IsActive:    req.IsActive,
		Conditions:  conditions,
		Actions:     actions,
	}

	ruleID, err := ac.automationService.CreateAutomationRule(userIDInt, automationReq)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Tạo quy tắc điều kiện thành công", gin.H{"rule_id": ruleID})
}

func (ac *AutomationController) CreateAutomationRule(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req models.CreateAutomationRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	ruleID, err := ac.automationService.CreateAutomationRule(userIDInt, &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Tạo quy tắc tự động thành công", gin.H{"rule_id": ruleID})
}

func (ac *AutomationController) GetUserRules(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	rules, err := ac.automationService.GetUserRules(userIDInt)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách quy tắc thành công", rules)
}

func (ac *AutomationController) GetRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	ruleIDStr := c.Param("rule_id")

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	rule, err := ac.automationService.GetRuleByID(ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quy tắc")
		return
	}

	if rule.UserID != userIDInt {
		utils.ErrorResponse(c, http.StatusForbidden, "Không có quyền truy cập quy tắc này")
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy thông tin quy tắc thành công", rule)
}

func (ac *AutomationController) UpdateRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	ruleIDStr := c.Param("rule_id")

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var req models.UpdateAutomationRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	err = ac.automationService.UpdateAutomationRule(userIDInt, ruleID, &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Cập nhật quy tắc thành công", nil)
}

func (ac *AutomationController) DeleteRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	ruleIDStr := c.Param("rule_id")

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	err = ac.automationService.DeleteRule(userIDInt, ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Xóa quy tắc thành công", nil)
}

func (ac *AutomationController) ToggleRuleStatus(c *gin.Context) {
	userID, _ := c.Get("user_id")
	ruleIDStr := c.Param("rule_id")

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var req struct {
		IsActive bool `json:"is_active"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	err = ac.automationService.UpdateRuleStatus(userIDInt, ruleID, req.IsActive)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	status := "tắt"
	if req.IsActive {
		status = "bật"
	}

	utils.SuccessResponse(c, http.StatusOK, "Đã "+status+" quy tắc thành công", nil)
}

func (ac *AutomationController) GetRuleTemplates(c *gin.Context) {
	templates, err := ac.automationService.GetRuleTemplates()
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách mẫu quy tắc thành công", templates)
}

func (ac *AutomationController) GetExecutionHistory(c *gin.Context) {
	userID, _ := c.Get("user_id")

	ruleIDStr := c.Query("rule_id")
	var ruleID int
	if ruleIDStr != "" {
		var err error
		ruleID, err = strconv.Atoi(ruleIDStr)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
			return
		}
	}

	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 50
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	history, err := ac.automationService.GetExecutionHistory(userIDInt, ruleID, limit)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy lịch sử thực thi thành công", history)
}


func (ac *AutomationController) CreateHomeAutomationRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var req models.CreateAutomationRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	req.HomeID = homeID

	ruleID, err := ac.automationService.CreateAutomationRule(userIDInt, &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Tạo quy tắc tự động thành công", gin.H{"rule_id": ruleID})
}

func (ac *AutomationController) GetHomeAutomationRules(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	rules, err := ac.automationService.GetHomeRules(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách quy tắc thành công", rules)
}

func (ac *AutomationController) GetHomeAutomationRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")
	ruleIDStr := c.Param("rule_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	rule, err := ac.automationService.GetHomeRule(userIDInt, homeID, ruleID)
	if err != nil {
		if err.Error() == "RULE_NOT_FOUND" {
			utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quy tắc")
		} else {
			utils.ErrorResponse(c, http.StatusForbidden, err.Error())
		}
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy thông tin quy tắc thành công", rule)
}

func (ac *AutomationController) UpdateHomeAutomationRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")
	ruleIDStr := c.Param("rule_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var req models.UpdateAutomationRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	_, err = ac.automationService.GetHomeRule(userIDInt, homeID, ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quy tắc trong nhà này")
		return
	}

	err = ac.automationService.UpdateAutomationRule(userIDInt, ruleID, &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Cập nhật quy tắc thành công", nil)
}

func (ac *AutomationController) DeleteHomeAutomationRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")
	ruleIDStr := c.Param("rule_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	_, err = ac.automationService.GetHomeRule(userIDInt, homeID, ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quy tắc trong nhà này")
		return
	}

	err = ac.automationService.DeleteRule(userIDInt, ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Xóa quy tắc thành công", nil)
}

func (ac *AutomationController) ToggleHomeAutomationRuleStatus(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")
	ruleIDStr := c.Param("rule_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var req struct {
		IsActive bool `json:"is_active"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	_, err = ac.automationService.GetHomeRule(userIDInt, homeID, ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quy tắc trong nhà này")
		return
	}

	err = ac.automationService.UpdateRuleStatus(userIDInt, ruleID, req.IsActive)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	status := "tắt"
	if req.IsActive {
		status = "bật"
	}

	utils.SuccessResponse(c, http.StatusOK, "Đã "+status+" quy tắc thành công", nil)
}

func (ac *AutomationController) ExecuteHomeAutomationRule(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")
	ruleIDStr := c.Param("rule_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	_, err = ac.automationService.GetHomeRule(userIDInt, homeID, ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quy tắc trong nhà này")
		return
	}

	result, err := ac.automationService.ExecuteHomeRule(userIDInt, homeID, ruleID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Thực thi quy tắc thành công", result)
}

func (ac *AutomationController) GetHomeAutomationExecutions(c *gin.Context) {
	userID, _ := c.Get("user_id")
	homeIDStr := c.Param("home_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	ruleIDStr := c.Query("rule_id")
	var ruleID int
	if ruleIDStr != "" {
		var err error
		ruleID, err = strconv.Atoi(ruleIDStr)
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
			return
		}
	}

	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 50
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if ruleID > 0 {
		_, err = ac.automationService.GetHomeRule(userIDInt, homeID, ruleID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quy tắc trong nhà này")
			return
		}
	}

	history, err := ac.automationService.GetExecutionHistory(userIDInt, ruleID, limit)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy lịch sử thực thi thành công", history)
}


func (ac *AutomationController) GetRuleExecutionHistory(c *gin.Context) {
	userID, _ := c.Get("user_id")
	ruleIDStr := c.Param("rule_id")

	ruleID, err := strconv.Atoi(ruleIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quy tắc không hợp lệ")
		return
	}

	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 50
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	history, err := ac.automationService.GetExecutionHistory(userIDInt, ruleID, limit)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy lịch sử thực thi quy tắc thành công", history)
}

func (ac *AutomationController) CreateRuleTemplate(c *gin.Context) {
	var req struct {
		Name         string `json:"name" binding:"required"`
		Description  string `json:"description"`
		RuleType     string `json:"rule_type" binding:"required"`
		TemplateData string `json:"template_data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	templateID := 1

	utils.SuccessResponse(c, http.StatusCreated, "Tạo template thành công", gin.H{"template_id": templateID})
}
