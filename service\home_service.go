package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/home"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/permission"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/user"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"
	"errors"
	"fmt"
	"log"
	"time"
)

type HomeService struct {
	homeRepo       home.Repository
	userRepo       user.Repository
	permissionRepo permission.Repository
}

func NewHomeService(homeRepo home.Repository, userRepo user.Repository, permissionRepo permission.Repository) *HomeService {
	return &HomeService{
		homeRepo:       homeRepo,
		userRepo:       userRepo,
		permissionRepo: permissionRepo,
	}
}

func (s *HomeService) CreateHome(userID int, req *models.HomeRequest) (int, error) {
	if _, err := s.userRepo.GetUserByID(userID); err != nil {
		return 0, errors.New("người dùng không tồn tại")
	}

	existingHomes, err := s.homeRepo.GetHomesByUserID(userID)
	if err != nil {
		return 0, errors.New("không thể kiểm tra homes hiện tại")
	}
	if len(existingHomes) > 0 {
		return 0, errors.New("mỗi tài khoản chỉ được tạo một nhà duy nhất")
	}

	home := &models.Home{
		Name:      req.Name,
		Address:   req.Address,
		CreatedAt: time.Now(),
	}

	homeID, err := s.homeRepo.CreateHome(home)
	if err != nil {
		return 0, errors.New("không thể tạo nhà")
	}

	homeUser := &models.HomeUser{
		HomeID:    homeID,
		UserID:    userID,
		RoleID:    1,
		CreatedAt: time.Now(),
	}

	if err := s.homeRepo.AddUserToHome(homeUser); err != nil {
		return 0, errors.New("không thể thêm người dùng vào nhà")
	}

	return homeID, nil
}

func (s *HomeService) GetHomeByID(userID int, homeID int) (*models.Home, error) {
	isInHome, err := s.homeRepo.IsUserInHome(homeID, userID)
	if err != nil {
		return nil, fmt.Errorf("lỗi kiểm tra quyền truy cập: %w", err)
	}
	if !isInHome {
		return nil, errors.New("bạn không có quyền truy cập nhà này")
	}

	home, err := s.homeRepo.GetHomeByID(homeID)
	if err != nil {
		return nil, fmt.Errorf("không thể lấy thông tin nhà: %w", err)
	}

	return home, nil
}

func (s *HomeService) GetHomeDetailWithStats(userID int, homeID int) (*models.HomeDetailResponse, error) {
	isInHome, err := s.homeRepo.IsUserInHome(homeID, userID)
	if err != nil {
		return nil, err
	}
	if !isInHome {
		return nil, errors.New("không có quyền truy cập nhà này")
	}

	home, err := s.homeRepo.GetHomeByID(homeID)
	if err != nil {
		return nil, err
	}

	stats, err := s.homeRepo.GetHomeStatistics(homeID)
	if err != nil {
		return nil, err
	}

	userRole, err := s.permissionRepo.GetUserRoleInHome(userID, homeID)
	if err != nil {
		return nil, err
	}

	roleName := "UNKNOWN"
	switch userRole {
	case 1:
		roleName = "OWNER"
	case 2:
		roleName = "ADMIN"
	case 3:
		roleName = "MEMBER"
	}

	response := &models.HomeDetailResponse{
		Home:         home,
		Statistics:   stats,
		UserRole:     userRole,
		UserRoleName: roleName,
	}

	return response, nil
}

func (s *HomeService) GetUserHomes(userID int) ([]models.Home, error) {
	return s.homeRepo.GetHomesByUserID(userID)
}

func (s *HomeService) UpdateHome(userID int, homeID int, req *models.HomeRequest) error {
	roleID, err := s.permissionRepo.GetUserRoleInHome(userID, homeID)
	if err != nil {
		return err
	}
	if roleID != 1 && roleID != 2 {
		return errors.New("chỉ OWNER hoặc ADMIN mới có quyền cập nhật thông tin nhà")
	}

	home, err := s.homeRepo.GetHomeByID(homeID)
	if err != nil {
		return err
	}

	home.Name = req.Name
	home.Address = req.Address

	return s.homeRepo.UpdateHome(home)
}

func (s *HomeService) DeleteHome(userID int, homeID int) error {
	roleID, err := s.permissionRepo.GetUserRoleInHome(userID, homeID)
	if err != nil {
		return err
	}
	if roleID != 1 {
		return errors.New("chỉ OWNER mới có quyền xóa nhà")
	}

	return s.homeRepo.DeleteHome(homeID)
}

func (s *HomeService) AddUserToHome(adminID int, homeID int, req *models.HomeUserRequest) error {
	adminRoleID, err := s.permissionRepo.GetUserRoleInHome(adminID, homeID)
	if err != nil {
		return err
	}
	if adminRoleID != 1 && adminRoleID != 2 {
		return errors.New("chỉ OWNER hoặc ADMIN mới có quyền thêm người dùng")
	}

	user, err := s.userRepo.GetUserByEmail(req.Email)
	if err != nil {
		return errors.New("người dùng với email này không tồn tại")
	}

	isInHome, err := s.homeRepo.IsUserInHome(homeID, user.UserID)
	if err != nil {
		return err
	}
	if isInHome {
		return errors.New("người dùng đã có trong nhà")
	}

	roleID := 3
	if req.RoleID > 0 && req.RoleID <= 3 {
		roleID = req.RoleID
	}

	homeUser := &models.HomeUser{
		HomeID:    homeID,
		UserID:    user.UserID,
		RoleID:    roleID,
		CreatedAt: time.Now(),
	}

	return s.homeRepo.AddUserToHome(homeUser)
}

func (s *HomeService) RemoveUserFromHome(adminID int, homeID int, userID int) error {
	canKick, err := s.permissionRepo.CheckUserCanKickUser(adminID, userID, homeID)
	if err != nil {
		return err
	}
	if !canKick {
		return errors.New("không có quyền kick user này")
	}

	return s.homeRepo.RemoveUserFromHome(homeID, userID)
}

func (s *HomeService) GetHomeUsers(userID int, homeID int) ([]models.User, error) {
	isInHome, err := s.homeRepo.IsUserInHome(homeID, userID)
	if err != nil {
		return nil, err
	}
	if !isInHome {
		return nil, errors.New("không có quyền truy cập nhà này")
	}

	return s.homeRepo.GetHomeUsers(homeID)
}

func (s *HomeService) GetHomeAreas(userID int, homeID int) ([]models.Area, error) {
	isInHome, err := s.homeRepo.IsUserInHome(homeID, userID)
	if err != nil {
		return nil, err
	}
	if !isInHome {
		return nil, errors.New("không có quyền truy cập nhà này")
	}

	return s.homeRepo.GetHomeAreas(homeID)
}

func (hs *HomeService) CreateArea(area *models.Area) (int, error) {
	_, err := hs.homeRepo.GetHomeByID(area.HomeID)
	if err != nil {
		return 0, errors.New("home not found")
	}

	return hs.homeRepo.CreateArea(area)
}

func (hs *HomeService) GetAreaByID(areaID int) (*models.Area, error) {
	return hs.homeRepo.GetAreaByID(areaID)
}

func (hs *HomeService) UpdateArea(area *models.Area) error {
	existingArea, err := hs.homeRepo.GetAreaByID(area.AreaID)
	if err != nil {
		return errors.New("area not found")
	}

	area.HomeID = existingArea.HomeID
	return hs.homeRepo.UpdateArea(area)
}

func (hs *HomeService) DeleteArea(areaID int) error {
	_, err := hs.homeRepo.GetAreaByID(areaID)
	if err != nil {
		return errors.New("area not found")
	}

	return hs.homeRepo.DeleteArea(areaID)
}

func (s *HomeService) PromoteUser(ownerID int, homeID int, req *models.PromoteUserRequest) error {
	ownerRoleID, err := s.permissionRepo.GetUserRoleInHome(ownerID, homeID)
	if err != nil {
		return err
	}
	if ownerRoleID != 1 {
		return errors.New("chỉ OWNER mới có quyền thăng cấp user")
	}

	_, err = s.permissionRepo.GetUserRoleInHome(req.UserID, homeID)
	if err != nil {
		return errors.New("user không thuộc home này")
	}

	if ownerID == req.UserID {
		return errors.New("không thể thăng cấp chính mình")
	}

	return s.permissionRepo.UpdateUserRoleInHome(req.UserID, homeID, req.RoleID)
}

func (s *HomeService) TransferOwnership(ownerID int, homeID int, req *models.TransferOwnershipRequest) error {
	ownerRoleID, err := s.permissionRepo.GetUserRoleInHome(ownerID, homeID)
	if err != nil {
		return err
	}
	if ownerRoleID != 1 {
		return errors.New("chỉ OWNER mới có quyền chuyển quyền sở hữu")
	}

	newOwnerRoleID, err := s.permissionRepo.GetUserRoleInHome(req.NewOwnerID, homeID)
	if err != nil {
		return errors.New("user mới không thuộc home này")
	}

	if newOwnerRoleID == 1 {
		return errors.New("user đã là OWNER")
	}

	if err := s.permissionRepo.UpdateUserRoleInHome(ownerID, homeID, 2); err != nil {
		return err
	}

	return s.permissionRepo.UpdateUserRoleInHome(req.NewOwnerID, homeID, 1)
}

func (s *HomeService) GetHomeUsersWithRoles(userID int, homeID int) ([]models.HomeUserResponse, error) {
	_, err := s.permissionRepo.GetUserRoleInHome(userID, homeID)
	if err != nil {
		return nil, errors.New("không có quyền truy cập nhà này")
	}

	users, err := s.homeRepo.GetHomeUsers(homeID)
	if err != nil {
		return nil, err
	}

	var result []models.HomeUserResponse
	for _, user := range users {
		roleID, err := s.permissionRepo.GetUserRoleInHome(user.UserID, homeID)
		if err != nil {
			continue
		}

		roleName := "UNKNOWN"
		switch roleID {
		case 1:
			roleName = "OWNER"
		case 2:
			roleName = "ADMIN"
		case 3:
			roleName = "MEMBER"
		}

		result = append(result, models.HomeUserResponse{
			UserID:   user.UserID,
			Email:    user.Email,
			FullName: user.FullName,
			RoleID:   roleID,
			RoleName: roleName,
		})
	}

	return result, nil
}

func (s *HomeService) SendInvitation(inviterID int, homeID int, req *models.HomeInvitationRequest) error {
	inviterRoleID, err := s.permissionRepo.GetUserRoleInHome(inviterID, homeID)
	if err != nil {
		return err
	}
	if inviterRoleID != 1 && inviterRoleID != 2 {
		return errors.New("chỉ OWNER hoặc ADMIN mới có quyền mời người dùng")
	}

	user, err := s.userRepo.GetUserByEmail(req.Email)
	if err != nil {
		return errors.New("người dùng với email này không tồn tại")
	}

	isInHome, err := s.homeRepo.IsUserInHome(homeID, user.UserID)
	if err != nil {
		return err
	}
	if isInHome {
		return errors.New("người dùng đã có trong nhà")
	}

	hasExisting, err := s.homeRepo.CheckExistingInvitation(homeID, req.Email)
	if err != nil {
		return err
	}
	if hasExisting {
		return errors.New("đã có lời mời đang chờ xác nhận cho email này")
	}

	home, err := s.homeRepo.GetHomeByID(homeID)
	if err != nil {
		return err
	}

	inviter, err := s.userRepo.GetUserByID(inviterID)
	if err != nil {
		return err
	}

	token, err := utils.GenerateInvitationToken()
	if err != nil {
		return fmt.Errorf("không thể tạo token: %v", err)
	}

	roleID := 3
	if req.RoleID > 0 && req.RoleID <= 3 {
		roleID = req.RoleID
	}

	invitation := &models.HomeInvitation{
		HomeID:    homeID,
		InviterID: inviterID,
		Email:     req.Email,
		RoleID:    roleID,
		Token:     token,
		Status:    "pending",
		ExpiresAt: time.Now().Add(24 * time.Hour),
		CreatedAt: time.Now(),
	}

	if err := s.homeRepo.CreateInvitation(invitation); err != nil {
		return fmt.Errorf("không thể tạo lời mời: %v", err)
	}

	acceptURL := fmt.Sprintf("http://localhost:8080/api/invitations/accept/%s", token)

	if err := utils.SendInvitationEmail(req.Email, inviter.FullName, home.Name, acceptURL); err != nil {
		s.homeRepo.DeleteInvitation(invitation.InvitationID)
		return fmt.Errorf("không thể gửi email mời: %v", err)
	}

	return nil
}

func (s *HomeService) AcceptInvitation(token string) error {
	invitation, err := s.homeRepo.GetInvitationByToken(token)
	if err != nil {
		return errors.New("lời mời không tồn tại hoặc đã hết hạn")
	}

	if invitation.Status != "pending" {
		return errors.New("lời mời đã được xử lý trước đó")
	}

	if time.Now().After(invitation.ExpiresAt) {
		s.homeRepo.UpdateInvitationStatus(invitation.InvitationID, "expired")
		return errors.New("lời mời đã hết hạn")
	}

	user, err := s.userRepo.GetUserByEmail(invitation.Email)
	if err != nil {
		return errors.New("người dùng không tồn tại")
	}

	isInHome, err := s.homeRepo.IsUserInHome(invitation.HomeID, user.UserID)
	if err != nil {
		return err
	}
	if isInHome {
		s.homeRepo.UpdateInvitationStatus(invitation.InvitationID, "accepted")
		return errors.New("bạn đã là thành viên của nhà này")
	}

	currentHome, err := s.homeRepo.GetUserHome(user.UserID)
	if err == nil && currentHome != nil {
		fmt.Printf("User %d đang rời khỏi nhà %d để vào nhà %d\n", user.UserID, currentHome.HomeID, invitation.HomeID)

		if err := s.homeRepo.RemoveUserFromHome(currentHome.HomeID, user.UserID); err != nil {
			return fmt.Errorf("không thể rời khỏi nhà hiện tại: %v", err)
		}

		isOwner, err := s.homeRepo.IsUserOwner(currentHome.HomeID, user.UserID)
		if err == nil && isOwner {
			memberCount, err := s.homeRepo.GetHomeMemberCount(currentHome.HomeID)
			if err == nil && memberCount == 0 {
				if err := s.homeRepo.DeleteHome(currentHome.HomeID); err != nil {
					fmt.Printf("Warning: Could not delete empty home %d: %v\n", currentHome.HomeID, err)
				} else {
					fmt.Printf("Đã xóa nhà trống %d\n", currentHome.HomeID)
				}
			}
		}
	}

	homeUser := &models.HomeUser{
		HomeID:    invitation.HomeID,
		UserID:    user.UserID,
		RoleID:    invitation.RoleID,
		CreatedAt: time.Now(),
	}

	if err := s.homeRepo.AddUserToHome(homeUser); err != nil {
		return fmt.Errorf("không thể thêm bạn vào nhà: %v", err)
	}

	if err := s.homeRepo.UpdateInvitationStatus(invitation.InvitationID, "accepted"); err != nil {
		fmt.Printf("Warning: Could not update invitation status: %v", err)
	}

	return nil
}

func (s *HomeService) ResendInvitation(userID int, homeID int, invitationID int) error {
	invitation, err := s.homeRepo.GetInvitationByToken("")
	if err != nil {
		invitations, err := s.homeRepo.GetInvitationsByHomeID(homeID)
		if err != nil {
			return errors.New("không thể lấy danh sách lời mời")
		}

		var foundInvitation *models.HomeInvitation
		for _, inv := range invitations {
			if inv.InvitationID == invitationID {
				foundInvitation = &inv
				break
			}
		}

		if foundInvitation == nil {
			return errors.New("không tìm thấy lời mời")
		}
		invitation = foundInvitation
	}

	if invitation.HomeID != homeID {
		return errors.New("lời mời không thuộc về nhà này")
	}

	if invitation.Status != "pending" {
		return errors.New("chỉ có thể gửi lại lời mời đang chờ xử lý")
	}

	home, err := s.homeRepo.GetHomeByID(homeID)
	if err != nil {
		return errors.New("không tìm thấy nhà")
	}

	inviter, err := s.userRepo.GetUserByID(invitation.InviterID)
	if err != nil {
		return errors.New("không tìm thấy người gửi lời mời")
	}

	newToken, err := utils.GenerateInvitationToken()
	if err != nil {
		return fmt.Errorf("không thể tạo token mới: %v", err)
	}

	newExpiresAt := time.Now().Add(24 * time.Hour)

	if err := s.homeRepo.UpdateInvitationToken(invitationID, newToken, newExpiresAt); err != nil {
		return fmt.Errorf("không thể cập nhật lời mời: %v", err)
	}

	acceptURL := fmt.Sprintf("http://localhost:8080/api/invitations/accept/%s", newToken)

	if err := utils.SendInvitationEmail(invitation.Email, inviter.FullName, home.Name, acceptURL); err != nil {
		return fmt.Errorf("không thể gửi email mời: %v", err)
	}

	return nil
}

func (s *HomeService) GetInvitationsByHomeID(userID int, homeID int) ([]models.HomeInvitationResponse, error) {
	roleID, err := s.permissionRepo.GetUserRoleInHome(userID, homeID)
	if err != nil {
		log.Printf(" [DEBUG] GetInvitationsByHomeID - Permission check failed for userID=%d, homeID=%d: %v", userID, homeID, err)
		return nil, errors.New("không có quyền truy cập nhà này")
	}
	log.Printf("[DEBUG] GetInvitationsByHomeID - UserID=%d, HomeID=%d, RoleID=%d", userID, homeID, roleID)

	if roleID != 1 && roleID != 2 {
		log.Printf("[DEBUG] GetInvitationsByHomeID - Insufficient permissions. RoleID=%d (need 1 or 2)", roleID)
		return nil, errors.New("chỉ OWNER hoặc ADMIN mới có quyền xem lời mời")
	}

	invitations, err := s.homeRepo.GetInvitationsByHomeID(homeID)
	if err != nil {
		log.Printf("[DEBUG] GetInvitationsByHomeID - Repository error: %v", err)
		return nil, err
	}
	log.Printf("[DEBUG] GetInvitationsByHomeID - Found %d invitations from repository", len(invitations))

	var result []models.HomeInvitationResponse
	for _, inv := range invitations {
		roleName := "UNKNOWN"
		switch inv.RoleID {
		case 1:
			roleName = "OWNER"
		case 2:
			roleName = "ADMIN"
		case 3:
			roleName = "MEMBER"
		}

		homeName := "Unknown Home"
		if inv.Home != nil {
			homeName = inv.Home.Name
		}

		inviterName := "Unknown User"
		if inv.Inviter != nil {
			inviterName = inv.Inviter.FullName
		}

		result = append(result, models.HomeInvitationResponse{
			InvitationID: inv.InvitationID,
			HomeID:       inv.HomeID,
			InviterID:    inv.InviterID,
			Email:        inv.Email,
			RoleID:       inv.RoleID,
			Token:        inv.Token,
			Status:       inv.Status,
			ExpiresAt:    inv.ExpiresAt,
			CreatedAt:    inv.CreatedAt,
			AcceptedAt:   inv.AcceptedAt,

			InviterName: inviterName,
			HomeName:    homeName,
			RoleName:    roleName,
		})
	}

	log.Printf(" [DEBUG] GetInvitationsByHomeID - Returning %d invitation responses", len(result))
	return result, nil
}
