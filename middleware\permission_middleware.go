package middleware

import (
	"dh52110724-api-quan-ly-nha-thong-minh/config"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/permission"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PermissionMiddleware struct {
	config         *config.Config
	permissionRepo permission.Repository
}

func NewPermissionMiddleware(config *config.Config, permissionRepo permission.Repository) *PermissionMiddleware {
	return &PermissionMiddleware{
		config:         config,
		permissionRepo: permissionRepo,
	}
}

func (pm *PermissionMiddleware) GetUserRoleInHome(userID, homeID int) (int, error) {
	return pm.permissionRepo.GetUserRoleInHome(userID, homeID)
}

func (pm *PermissionMiddleware) RequireOwner() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
			c.Abort()
			return
		}

		homeID, err := strconv.Atoi(c.Param("home_id"))
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
			c.Abort()
			return
		}

		roleID, err := pm.GetUserRoleInHome(int(userID.(uint)), homeID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi kiểm tra quyền")
			c.Abort()
			return
		}

		if roleID != 1 {
			utils.ErrorResponse(c, http.StatusForbidden, "Chỉ OWNER mới có quyền thực hiện hành động này")
			c.Abort()
			return
		}

		c.Set("user_role", roleID)
		c.Next()
	}
}

func (pm *PermissionMiddleware) RequireAdminOrOwner() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
			c.Abort()
			return
		}

		homeID, err := strconv.Atoi(c.Param("home_id"))
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
			c.Abort()
			return
		}

		roleID, err := pm.GetUserRoleInHome(int(userID.(uint)), homeID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi kiểm tra quyền")
			c.Abort()
			return
		}

		if roleID != 1 && roleID != 2 {
			utils.ErrorResponse(c, http.StatusForbidden, "Chỉ ADMIN hoặc OWNER mới có quyền thực hiện hành động này")
			c.Abort()
			return
		}

		c.Set("user_role", roleID)
		c.Next()
	}
}

func (pm *PermissionMiddleware) RequireAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
			c.Abort()
			return
		}

		homeID, err := strconv.Atoi(c.Param("home_id"))
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
			c.Abort()
			return
		}

		roleID, err := pm.GetUserRoleInHome(int(userID.(uint)), homeID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi kiểm tra quyền")
			c.Abort()
			return
		}

		if roleID != 2 {
			utils.ErrorResponse(c, http.StatusForbidden, "Chỉ ADMIN mới có quyền thực hiện hành động này")
			c.Abort()
			return
		}

		c.Set("user_role", roleID)
		c.Next()
	}
}

func (pm *PermissionMiddleware) RequireMember() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
			c.Abort()
			return
		}

		homeID, err := strconv.Atoi(c.Param("home_id"))
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
			c.Abort()
			return
		}

		roleID, err := pm.GetUserRoleInHome(int(userID.(uint)), homeID)
		// if err != nil {
		// 	utils.ErrorResponse(c, http.StatusForbidden, "Bạn không thuộc nhà này")
		// 	c.Abort()
		// 	return
		// }

		// if roleID < 1 || roleID > 3 {
		// 	utils.ErrorResponse(c, http.StatusForbidden, "Role không hợp lệ")
		// 	c.Abort()
		// 	return
		// }

		c.Set("user_role", roleID)
		c.Next()
	}
}

func (pm *PermissionMiddleware) CheckDevicePermission() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, _ := c.Get("user_id")
		deviceID, err := strconv.Atoi(c.Param("id"))
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID thiết bị không hợp lệ")
			c.Abort()
			return
		}

		hasPermission, err := pm.permissionRepo.CheckDevicePermission(int(userID.(uint)), deviceID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi kiểm tra quyền")
			c.Abort()
			return
		}

		if !hasPermission {
			utils.ErrorResponse(c, http.StatusForbidden, "Không có quyền thao tác với thiết bị này")
			c.Abort()
			return
		}

		c.Next()
	}
}

func (pm *PermissionMiddleware) CheckAreaPermission() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, _ := c.Get("user_id")
		areaID, err := strconv.Atoi(c.Param("id"))
		if err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "ID khu vực không hợp lệ")
			c.Abort()
			return
		}

		hasPermission, err := pm.permissionRepo.CheckAreaPermission(int(userID.(uint)), areaID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi kiểm tra quyền")
			c.Abort()
			return
		}

		if !hasPermission {
			utils.ErrorResponse(c, http.StatusForbidden, "Không có quyền thao tác với khu vực này")
			c.Abort()
			return
		}

		c.Next()
	}
}
