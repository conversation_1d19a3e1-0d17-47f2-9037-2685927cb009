package device

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
)

type Repository interface {
	CreateDevice(device *models.Device) (int, error)
	GetDeviceByID(deviceID int) (*models.Device, error)
	GetDevicesByUserPermission(userID int) ([]models.Device, error)
	GetDevicesByAreaID(areaID int) ([]models.Device, error)
	GetDevicesByHomeID(homeID int) ([]models.Device, error)
	GetDevicesByTypeInHome(homeID int, deviceTypeID int) ([]models.Device, error)
	GetDevicesWithoutHome() ([]models.Device, error)
	GetDevicesInHomeWithoutArea(homeID int) ([]models.Device, error)
	UpdateDevice(device *models.Device) error
	UpdateDeviceArea(deviceID int, areaID *int) error
	UpdateDeviceStatus(deviceID int, status string) error
	UpdateDeviceOnlineStatus(deviceID int, isOnline bool) error
	DeleteDevice(deviceID int) error
	DeviceExists(deviceID int) bool
	CheckDeviceExists(uniqueIdentifier string) (bool, error)
	GetDeviceByUniqueIdentifier(uniqueIdentifier string) (*models.Device, error)
	RemoveDeviceFromHome(deviceID int) error
	DeviceTypeExists(deviceTypeID int) bool
	GetDeviceTypeByID(deviceTypeID int) (*models.DeviceType, error)
	GetAllDeviceTypes() ([]models.DeviceType, error)

	AreaExists(areaID int) bool
	AreaExistsInHome(areaID int, homeID int) bool
	GetAreaByID(areaID int) (*models.Area, error)
	GetHomeIDByAreaID(areaID int) (uint, error)

	PropertyExists(propertyID int) bool
	GetPropertyByID(propertyID int) (*models.DeviceProperty, error)
	GetPropertiesByDeviceType(deviceTypeID int) ([]models.DeviceProperty, error)
	GetDeviceProperties(deviceID int) ([]models.DeviceProperty, error)
	GetPropertyIDByName(deviceID int, propertyName string) (int, error)
	GetPropertyIDByTemplate(deviceID int, templateID int) (int, error)
	CreateProperty(property *models.DeviceProperty) (int, error)
	UpdateProperty(property *models.DeviceProperty) error

	UpdateDeviceState(deviceID int, propertyID int, value string) error
	GetDeviceStates(deviceID int) ([]models.DeviceState, error)
	GetCurrentPropertyValue(deviceID int, propertyID int) (string, error)
	UpdateDeviceProperty(deviceID int, propertyName string, value interface{}) error

	CreateCommand(command *models.DeviceCommand) error
	UpdateCommandStatus(commandID int, status string) error
	GetDeviceCommandHistory(deviceID int, limit int) ([]models.DeviceCommand, error)
	GetDeviceCommands(deviceID int) ([]models.DeviceCommand, error)

	GetConnectionTypeByID(connectionTypeID int) (*models.ConnectionType, error)
	GetAllConnectionTypes() ([]models.ConnectionType, error)
	GetConnectionTypes() ([]models.ConnectionType, error)

	CreateNetworkInfo(networkInfo *models.DeviceNetworkInfo) error
	IsDeviceRegisteredInHome(mac string, homeID int) (bool, error)



	GetRealTimeDeviceStatus(deviceID int) (*models.RealTimeDeviceStatus, error)
	GetAllRealTimeDeviceStatus(userID int) ([]models.RealTimeDeviceStatus, error)
	GetAllRealTimeDeviceStatusByHome(homeID int) ([]models.RealTimeDeviceStatus, error)

	ExecuteBatchControl(commands []models.DeviceCommand) ([]models.BatchControlResult, error)

	IsDeviceUsedInRules(deviceID int) (bool, error)
}
