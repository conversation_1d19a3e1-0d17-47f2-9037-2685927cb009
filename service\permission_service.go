package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/permission"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/user"
	"errors"
	"log"
	"time"
)

type PermissionService struct {
	permRepo permission.Repository
	userRepo user.Repository
}

func NewPermissionService(permRepo permission.Repository, userRepo user.Repository) *PermissionService {
	return &PermissionService{
		permRepo: permRepo,
		userRepo: userRepo,
	}
}

func (s *PermissionService) GetUserPermissions(userID int) ([]models.Permission, error) {
	_, err := s.userRepo.GetUserByID(userID)
	if err != nil {
		return nil, errors.New("người dùng không tồn tại")
	}

	return s.permRepo.GetUserPermissions(userID)
}

func (s *PermissionService) AddDevicePermission(permission *models.Permission) error {
	if _, err := s.userRepo.GetUserByID(permission.UserID); err != nil {
		return errors.New("người dùng không tồn tại")
	}

	if permission.DeviceID == nil {
		return errors.New("thiết bị không được để trống")
	}

	exists, err := s.permRepo.CheckDevicePermission(permission.UserID, *permission.DeviceID)
	if err != nil {
		return err
	}
	if exists {
		return errors.New("quyền đã tồn tại")
	}

	return s.permRepo.AddDevicePermission(permission)
}

func (s *PermissionService) AddAreaPermission(permission *models.Permission) error {
	if _, err := s.userRepo.GetUserByID(permission.UserID); err != nil {
		return errors.New("người dùng không tồn tại")
	}

	if permission.AreaID == nil {
		return errors.New("khu vực không được để trống")
	}

	exists, err := s.permRepo.CheckAreaPermission(permission.UserID, *permission.AreaID)
	if err != nil {
		return err
	}
	if exists {
		return errors.New("quyền đã tồn tại")
	}

	return s.permRepo.AddAreaPermission(permission)
}

func (s *PermissionService) RemovePermission(permissionID int) error {
	permission, err := s.permRepo.GetPermissionByID(permissionID)
	if err != nil {
		return errors.New("quyền không tồn tại")
	}

	if permission.DeviceID != nil {
		return s.permRepo.RemoveDevicePermission(permission.UserID, *permission.DeviceID)
	}

	if permission.AreaID != nil {
		return s.permRepo.RemoveAreaPermission(permission.UserID, *permission.AreaID)
	}

	return errors.New("quyền không hợp lệ")
}

func (s *PermissionService) UpdatePermission(permissionID int, canView, canControl bool) error {
	log.Printf("Service: Updating permission ID %d - CanView: %t, CanControl: %t",
		permissionID, canView, canControl)

	_, err := s.permRepo.GetPermissionByID(permissionID)
	if err != nil {
		log.Printf("Service: Permission not found: %v", err)
		return err
	}

	err = s.permRepo.UpdatePermission(permissionID, canView, canControl)
	if err != nil {
		log.Printf("Service: Update failed: %v", err)
		return err
	}

	log.Printf("Service: Permission updated successfully")
	return nil
}

func (s *PermissionService) GetUserDevices(userID int) ([]models.Device, error) {
	permissions, err := s.permRepo.GetUserPermissions(userID)
	if err != nil {
		return nil, err
	}

	var devices []models.Device
	for _, perm := range permissions {
		if perm.DeviceID != nil {
		}
	}

	return devices, nil
}

func (s *PermissionService) UpdateUserDevice(userID int, deviceID int, propertyID int, value string) error {
	hasPermission, err := s.permRepo.CheckDevicePermission(userID, deviceID)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("không có quyền điều khiển thiết bị này")
	}

	deviceState := &models.DeviceState{
		DeviceID:   deviceID,
		PropertyID: propertyID,
		Value:      value,
		Timestamp:  time.Now(),
	}

	return s.userRepo.UpdateDeviceState(deviceState)
}

func (s *PermissionService) AssignUserRole(userID int, roleName string) error {
	_, err := s.userRepo.GetUserByID(userID)
	if err != nil {
		return errors.New("người dùng không tồn tại")
	}

	roles, err := s.permRepo.GetUserRoles(userID)
	if err != nil {
		return err
	}

	for _, role := range roles {
		if role.Name == roleName {
			return errors.New("người dùng đã có role này")
		}
	}

	return s.userRepo.AssignRole(userID, roleName)
}

func (s *PermissionService) AssignAreaPermission(adminID int, userID int, areaID int, canView bool, canControl bool, canConfigure bool) error {
	isAdmin, err := s.userRepo.IsUserAdmin(adminID)
	if err != nil {
		return err
	}
	if !isAdmin {
		return errors.New("không có quyền phân quyền")
	}

	permission := &models.Permission{
		UserID: userID,
		AreaID: &areaID,
	}

	_ = canView
	_ = canControl
	_ = canConfigure

	return s.permRepo.AddAreaPermission(permission)
}

func (s *PermissionService) GetPermissionByID(permissionID int) (*models.Permission, error) {
	return s.permRepo.GetPermissionByID(permissionID)
}
func (s *PermissionService) GetPermissionsByHome(homeID int) ([]models.Permission, error) {
	return s.permRepo.GetPermissionsByHome(homeID)
}

func (s *PermissionService) GetPermissionsByHomeWithFilter(homeID int, filterType string) ([]models.Permission, error) {
	return s.permRepo.GetPermissionsByHomeWithFilter(homeID, filterType)
}

func (s *PermissionService) RemovePermissionAsAdmin(adminUserID int, homeID int, permissionID int) error {
	permission, err := s.permRepo.GetPermissionByID(permissionID)
	if err != nil {
		return errors.New("permission not found")
	}

	targetUserRole, err := s.permRepo.GetUserRoleInHome(permission.UserID, homeID)
	if err != nil {
		return errors.New("không thể kiểm tra role của user")
	}

	if targetUserRole != 3 {
		return errors.New("ADMIN chỉ có thể xóa quyền của MEMBER")
	}

	if permission.DeviceID != nil {
		return s.permRepo.RemoveDevicePermission(permission.UserID, *permission.DeviceID)
	}

	if permission.AreaID != nil {
		return s.permRepo.RemoveAreaPermission(permission.UserID, *permission.AreaID)
	}

	return errors.New("quyền không hợp lệ")
}
