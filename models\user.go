package models

import (
	"time"
)

type User struct {
	UserID       int        `json:"user_id" gorm:"primaryKey;column:user_id"`
	Email        string     `json:"email" gorm:"column:email;type:varchar(255);uniqueIndex"`
	PhoneNumber  *string    `json:"phone_number" gorm:"column:phone_number;type:varchar(255);uniqueIndex"`
	PasswordHash string     `json:"-" gorm:"column:password_hash;type:varchar(255)"`
	FullName     string     `json:"full_name" gorm:"column:full_name;type:varchar(255)"`
	IsVerified   bool       `json:"is_verified" gorm:"column:is_verified;default:0"`
	CreatedAt    time.Time  `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time  `json:"updated_at" gorm:"column:updated_at;default:CURRENT_TIMESTAMP"`
	LastLogin    *time.Time `json:"last_login" gorm:"column:last_login;default:null"`
}

func (User) TableName() string {
	return "users"
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type RegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

type VerifyOTPRequest struct {
	Email string `json:"email" binding:"required,email"`
	OTP   string `json:"otp" binding:"required"`
}
type LoginResponse struct {
	Message     string `json:"message"`
	RequiresOTP bool   `json:"requires_otp,omitempty"`
	Email       string `json:"email,omitempty"`
	Token       string `json:"token,omitempty"`
	User        *User  `json:"user,omitempty"`
}

type RegisterResponse struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Email   string `json:"email"`
}

type UserResponse struct {
	UserID      int        `json:"user_id"`
	Email       string     `json:"email"`
	PhoneNumber *string    `json:"phone_number"`
	FullName    string     `json:"full_name"`
	IsActive    bool       `json:"is_active"`
	IsVerified  bool       `json:"is_verified"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	LastLogin   *time.Time `json:"last_login"`
}

type ResendOTPRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type VerifyOTPResponse struct {
	Message string       `json:"message"`
	Success bool         `json:"success"`
	Token   string       `json:"token"`
	User    UserResponse `json:"user"`
}

type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type ResetPasswordRequest struct {
	Email       string `json:"email" binding:"required,email"`
	OTP         string `json:"otp" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

type UpdateProfileRequest struct {
	FullName    string  `json:"full_name,omitempty"`
	Email       string  `json:"email,omitempty" binding:"omitempty,email"`
	PhoneNumber *string `json:"phone_number,omitempty"`
	HomeName    string  `json:"home_name,omitempty"`
	HomeAddress string  `json:"home_address,omitempty"`
}
