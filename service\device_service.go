package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/device"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/permission"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"
)

type DeviceService struct {
	deviceRepo     device.Repository
	permissionRepo permission.Repository
	mqttService    *MQTTService
}

func NewDeviceService(deviceRepo device.Repository, permissionRepo permission.Repository) *DeviceService {
	return &DeviceService{
		deviceRepo:     deviceRepo,
		permissionRepo: permissionRepo,
	}
}

func (s *DeviceService) SetWebSocketService(ws interface{}) {
}

func (s *DeviceService) SetMQTTService(mqttService *MQTTService) {
	s.mqttService = mqttService
}

func (s *DeviceService) GetMQTTStatus() map[string]interface{} {
	if s.mqttService == nil {
		return map[string]interface{}{
			"connected": false,
			"error":     "MQTT service not initialized",
		}
	}
	return s.mqttService.GetConnectionStatus()
}

func (s *DeviceService) GetMQTTHeartbeatStatus() map[string]interface{} {
	if s.mqttService == nil {
		return map[string]interface{}{
			"error": "MQTT service not initialized",
		}
	}
	return s.mqttService.GetHeartbeatStatus()
}

func (s *DeviceService) ForceCheckAllDevicesTimeout() {
	if s.mqttService != nil {
		s.mqttService.ForceCheckAllDevicesTimeout()
	}
}

func (s *DeviceService) TestMQTTPublish(topic string, payload interface{}) error {
	if s.mqttService == nil {
		return fmt.Errorf("MQTT service not initialized")
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %v", err)
	}

	token := s.mqttService.client.Publish(topic, 1, false, jsonData)
	token.Wait()

	return token.Error()
}

func (s *DeviceService) AddDeviceToHome(req *models.AddDeviceToHomeRequest, homeID int, userID int) (*models.Device, error) {

	existingDevice, err := s.deviceRepo.GetDeviceByUniqueIdentifier(req.UniqueIdentifier)
	if err == nil && existingDevice != nil {
		if existingDevice.HomeID != nil {
			return nil, errors.New("thiết bị đã được đăng ký trong hệ thống")
		} else {
			return nil, errors.New("thiết bị đã tồn tại nhưng chưa được thêm vào nhà nào, vui lòng liên hệ admin")
		}
	}

	if req.DeviceTypeID != nil {
		if !s.deviceRepo.DeviceTypeExists(*req.DeviceTypeID) {
			if *req.DeviceTypeID < 6 || *req.DeviceTypeID > 23 {
				return nil, errors.New("loại thiết bị không tồn tại")
			}
			fmt.Printf("⚠ Device type %d chưa có trong database, nhưng được accept cho ESP32\n", *req.DeviceTypeID)
		}
	}

	if req.AreaID != nil {
		if !s.deviceRepo.AreaExistsInHome(*req.AreaID, homeID) {
			return nil, errors.New("khu vực không tồn tại hoặc không thuộc nhà này")
		}
	}

	device := &models.Device{
		Name:             req.Name,
		DeviceTypeID:     req.DeviceTypeID,
		ConnectionTypeID: req.ConnectionTypeID,
		UniqueIdentifier: req.UniqueIdentifier,
		HomeID:           &homeID,
		AreaID:           req.AreaID,
		IsOnline:         true,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	deviceID, err := s.deviceRepo.CreateDevice(device)
	if err != nil {
		return nil, fmt.Errorf("không thể tạo thiết bị: %v", err)
	}
	device.DeviceID = deviceID

	if req.NetworkInfo != nil {
		req.NetworkInfo.DeviceID = deviceID
		now := time.Now()
		req.NetworkInfo.LastConnected = &now

		if err := s.deviceRepo.CreateNetworkInfo(req.NetworkInfo); err != nil {
			fmt.Printf(" Không thể lưu network info: %v\n", err)
		}
	}

	return device, nil
}

func (s *DeviceService) GetAvailableDevices(userID int) ([]models.Device, error) {
	return s.deviceRepo.GetDevicesWithoutHome()
}

func (s *DeviceService) ControlDevice(userID int, deviceID int, command string, value string) error {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("không tìm thấy thiết bị")
	}

	if userID != 0 {
		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, deviceID)
		if err != nil {
			return err
		}
		if !hasPermission {
			return errors.New("không có quyền điều khiển thiết bị này")
		}
	}

	if !device.IsOnline {
		return errors.New("thiết bị không trực tuyến")
	}

	commandLog := &models.DeviceCommand{
		DeviceID:  deviceID,
		UserID:    userID,
		Command:   command,
		Value:     value,
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	if err := s.deviceRepo.CreateCommand(commandLog); err != nil {
		return errors.New("không thể ghi lại lệnh điều khiển")
	}

	if err := s.sendCommandToDevice(device, command, value, userID); err != nil {
		s.deviceRepo.UpdateCommandStatus(commandLog.CommandID, "failed")
		return err
	}

	s.deviceRepo.UpdateCommandStatus(commandLog.CommandID, "success")

	return nil
}

func (s *DeviceService) GetUserDevices(userID int) ([]models.Device, error) {
	return s.deviceRepo.GetDevicesByUserPermission(userID)
}

func (s *DeviceService) GetDevicesByArea(userID int, areaID int) ([]models.Device, error) {
	hasPermission, err := s.permissionRepo.CheckAreaViewPermission(userID, areaID)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New("không có quyền truy cập khu vực này")
	}

	return s.deviceRepo.GetDevicesByAreaID(areaID)
}

func (s *DeviceService) UpdateDeviceStatus(deviceID int, status string) error {
	if !s.deviceRepo.DeviceExists(deviceID) {
		return errors.New("thiết bị không tồn tại")
	}

	validStatuses := []string{"on", "off", "active", "inactive", "maintenance", "offline"}
	isValidStatus := false
	for _, validStatus := range validStatuses {
		if status == validStatus {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return errors.New("trạng thái không hợp lệ. Chỉ chấp nhận: on, off, active, inactive, maintenance, offline")
	}

	return s.deviceRepo.UpdateDeviceStatus(deviceID, status)
}

func (s *DeviceService) GetDeviceHistory(userID int, deviceID int, limit int) ([]models.DeviceCommand, error) {

	return s.deviceRepo.GetDeviceCommandHistory(deviceID, limit)
}

func (s *DeviceService) GetDeviceStates(userID int, deviceID int) ([]models.DeviceState, error) {
	hasPermission, err := s.permissionRepo.CheckDeviceViewPermission(userID, deviceID)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New("không có quyền xem trạng thái thiết bị này")
	}

	return s.deviceRepo.GetDeviceStates(deviceID)
}

func (s *DeviceService) UpdateDevice(userID int, deviceID int, req *models.UpdateDeviceRequest) error {
	hasPermission, err := s.permissionRepo.CheckDeviceConfigPermission(userID, deviceID)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("không có quyền cấu hình thiết bị này")
	}

	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("không tìm thấy thiết bị")
	}

	if req.Name != "" {
		device.Name = req.Name
	}
	if req.AreaID != nil {
		if *req.AreaID == 0 {
			device.AreaID = nil
		} else {
			if !s.deviceRepo.AreaExists(*req.AreaID) {
				return errors.New("khu vực mới không tồn tại")
			}
			device.AreaID = req.AreaID
		}
	}
	if req.Status != "" {
		validStatuses := []string{"on", "off", "active", "inactive", "maintenance", "offline"}
		isValidStatus := false
		for _, validStatus := range validStatuses {
			if req.Status == validStatus {
				isValidStatus = true
				break
			}
		}
		if !isValidStatus {
			return errors.New("trạng thái không hợp lệ. Chỉ chấp nhận: on, off, active, inactive, maintenance, offline")
		}
		device.Status = req.Status
	}

	device.UpdatedAt = time.Now()

	return s.deviceRepo.UpdateDevice(device)
}

func (s *DeviceService) DeleteDevice(userID int, deviceID int) error {
	hasPermission, err := s.permissionRepo.CheckDeviceConfigPermission(userID, deviceID)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("không có quyền xóa thiết bị này")
	}

	return s.deviceRepo.DeleteDevice(deviceID)
}

func (s *DeviceService) sendCommandToDevice(device *models.Device, command string, value string, userID int) error {


	if s.mqttService == nil || !s.mqttService.IsConnected() {
		return fmt.Errorf("MQTT service không khả dụng")
	}

	if device.HomeID == nil {
		return fmt.Errorf("thiết bị chưa được thêm vào nhà nào")
	}

	fmt.Printf("Sending MQTT command to ESP32: %s -> %s=%s\n", device.UniqueIdentifier, command, value)
	fmt.Printf("Device Debug Info: ID=%d, UniqueID=%s, HomeID=%d\n", device.DeviceID, device.UniqueIdentifier, *device.HomeID)
	if err := s.mqttService.PublishCommandWithUniqueID(*device.HomeID, device.DeviceID, device.UniqueIdentifier, command, value); err != nil {
		return fmt.Errorf("failed to send MQTT command: %v", err)
	}

	s.updateDeviceStateAfterCommand(device, command, value)

	fmt.Printf("MQTT command sent successfully to %s\n", device.UniqueIdentifier)
	return nil
}

func (s *DeviceService) getDeviceTypeName(deviceTypeID int) string {
	deviceTypeNames := map[int]string{
		1: "Smart Light",
		2: "Smart Switch",
		3: "Camera",
		4: "Temperature Sensor",
		5: "Smart AC",
	}

	if name, exists := deviceTypeNames[deviceTypeID]; exists {
		return name
	}
	return "Unknown Device"
}

func (s *DeviceService) getPropertyIDFromCommand(command string) int {
	commandToPropertyMap := map[string]int{
		"set_temperature": 3,
		"set_brightness":  2,
		"turn_on":         1,
		"turn_off":        1,
		"toggle":          1,
	}

	if propertyID, exists := commandToPropertyMap[command]; exists {
		return propertyID
	}
	return 1
}

func (s *DeviceService) updateDeviceStateAfterCommand(device *models.Device, command string, value string) {
	switch command {
	case "turn_on":
		device.Status = "on"
	case "turn_off":
		device.Status = "off"
	case "toggle":
		if device.Status == "on" {
			device.Status = "off"
		} else {
			device.Status = "on"
		}
	case "set_brightness":
		if value != "" && value != "0" {
			device.Status = "on"
		} else {
			device.Status = "off"
		}
	}

	device.UpdatedAt = time.Now()

	fmt.Printf(" Updating device status: ID=%d, Status=%s\n", device.DeviceID, device.Status)

	if err := s.deviceRepo.UpdateDevice(device); err != nil {
		fmt.Printf(" Failed to update device status: %v\n", err)
	} else {
		fmt.Printf(" Device status updated successfully: ID=%d, Status=%s\n", device.DeviceID, device.Status)
	}
}

func (s *DeviceService) controlSmartLight(device *models.Device, command string, value string) error {
	switch command {
	case "turn_on":
		return nil
	case "turn_off":
		return nil
	case "set_brightness":
		return nil
	default:
		return errors.New("lệnh không được hỗ trợ cho đèn thông minh. Các lệnh hỗ trợ: turn_on, turn_off")
	}
}

func (s *DeviceService) controlSmartSwitch(device *models.Device, command string, value string) error {
	switch command {
	case "turn_on":
		return nil
	case "turn_off":
		return nil
	case "set_brightness":
		if value == "" {
			return errors.New("giá trị độ sáng không được để trống")
		}
		return nil
	case "toggle":
		return nil
	default:
		return errors.New("lệnh không được hỗ trợ cho công tắc thông minh. Các lệnh hỗ trợ: turn_on, turn_off")
	}
}

func (s *DeviceService) GetRealTimeDeviceStatus(userID int, deviceID int) (*models.RealTimeDeviceStatus, error) {

	return s.deviceRepo.GetRealTimeDeviceStatus(deviceID)
}

func (s *DeviceService) GetAllRealTimeDeviceStatus(userID int) ([]models.RealTimeDeviceStatus, error) {
	return s.deviceRepo.GetAllRealTimeDeviceStatus(userID)
}

func (s *DeviceService) BatchControlDevices(userID int, req *models.BatchControlRequest) (*models.BatchControlResponse, error) {
	var commands []models.DeviceCommand
	var results []models.BatchControlResult

	for _, cmd := range req.DeviceCommands {
		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, cmd.DeviceID)
		if err != nil || !hasPermission {
			results = append(results, models.BatchControlResult{
				DeviceID: cmd.DeviceID,
				Success:  false,
				Error:    "Không có quyền điều khiển thiết bị này",
			})
			continue
		}

		command := models.DeviceCommand{
			DeviceID:  cmd.DeviceID,
			UserID:    userID,
			Command:   cmd.Command,
			Value:     cmd.Value,
			Status:    "pending",
			CreatedAt: time.Now(),
		}
		commands = append(commands, command)
	}

	if req.ExecuteSequentially {
		for _, command := range commands {
			err := s.ControlDevice(userID, command.DeviceID, command.Command, command.Value)
			result := models.BatchControlResult{
				DeviceID: command.DeviceID,
				Success:  err == nil,
			}
			if err != nil {
				result.Error = err.Error()
			} else {
				result.Message = "Thành công"
			}
			results = append(results, result)
		}
	} else {
		batchResults, err := s.deviceRepo.ExecuteBatchControl(commands)
		if err != nil {
			return nil, err
		}
		results = append(results, batchResults...)
	}

	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	response := &models.BatchControlResponse{
		Status:  "completed",
		Message: fmt.Sprintf("Đã thực hiện %d/%d lệnh thành công", successCount, len(results)),
		Results: results,
	}

	return response, nil
}

func (s *DeviceService) UpdateDeviceProperty(userID int, deviceID int, propertyName string, value interface{}) error {
	hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, deviceID)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("không có quyền điều khiển thiết bị này")
	}

	err = s.deviceRepo.UpdateDeviceProperty(deviceID, propertyName, value)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) MonitorDeviceStatus(deviceID int) error {

	go func() {
		for {
			time.Sleep(30 * time.Second)

		}
	}()

	return nil
}

func (s *DeviceService) GetDevicesByUser(userID int) ([]models.Device, error) {
	return s.deviceRepo.GetDevicesByUserPermission(userID)
}

func (s *DeviceService) GetDeviceByID(userID, deviceID int) (*models.Device, error) {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return nil, fmt.Errorf("không tìm thấy thiết bị: %v", err)
	}

	return device, nil
}

func (s *DeviceService) GetDevicesByUserID(userID int) ([]models.Device, error) {
	devices, err := s.deviceRepo.GetDevicesByUserPermission(userID)
	if err != nil {
		return nil, fmt.Errorf("lỗi khi lấy danh sách thiết bị: %v", err)
	}

	return devices, nil
}

func (s *DeviceService) GetAllDeviceTypes() ([]models.DeviceType, error) {
	return s.deviceRepo.GetAllDeviceTypes()
}

func (s *DeviceService) GetDeviceTypeByID(deviceTypeID int) (*models.DeviceType, error) {
	return s.deviceRepo.GetDeviceTypeByID(deviceTypeID)
}

func (s *DeviceService) GetDeviceProperties(userID int, deviceID int) ([]models.DeviceProperty, error) {

	return s.deviceRepo.GetDeviceProperties(deviceID)
}

func (s *DeviceService) GetDeviceCommands(userID int, deviceID int) ([]models.DeviceCommand, error) {
	hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, deviceID)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New("không có quyền điều khiển thiết bị này")
	}

	return s.deviceRepo.GetDeviceCommands(deviceID)
}
func (ds *DeviceService) AddDeviceToArea(deviceID int, areaID int) error {
	device, err := ds.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("device not found")
	}

	device.AreaID = &areaID
	return ds.deviceRepo.UpdateDevice(device)
}

func (ds *DeviceService) RemoveDeviceFromArea(deviceID int) error {
	device, err := ds.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("device not found")
	}

	device.AreaID = nil
	return ds.deviceRepo.UpdateDevice(device)
}

func (ds *DeviceService) GetDevicesInArea(areaID int) ([]models.Device, error) {
	return ds.deviceRepo.GetDevicesByAreaID(areaID)
}

func (ds *DeviceService) GetDevicesByHomeID(userID, homeID int) ([]models.Device, error) {
	return ds.deviceRepo.GetDevicesByHomeID(homeID)
}

func (ds *DeviceService) GetDevicesByTypeInHome(userID, homeID, deviceTypeID int) ([]models.Device, error) {
	hasPermission, err := ds.permissionRepo.CheckHomeViewPermission(userID, homeID)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New("không có quyền truy cập nhà này")
	}

	if !ds.deviceRepo.DeviceTypeExists(deviceTypeID) {
		return nil, errors.New("loại thiết bị không tồn tại")
	}

	return ds.deviceRepo.GetDevicesByTypeInHome(homeID, deviceTypeID)
}

func (s *DeviceService) GetDevicesInHomeWithoutArea(userID int, homeID int) ([]models.Device, error) {
	hasPermission, err := s.permissionRepo.CheckHomeViewPermission(userID, homeID)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New("không có quyền truy cập nhà này")
	}

	return s.deviceRepo.GetDevicesInHomeWithoutArea(homeID)
}

func (ds *DeviceService) ControlDeviceInHome(userID, homeID, deviceID int, command, value string) error {
	device, err := ds.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return err
	}
	if device.HomeID == nil || *device.HomeID != homeID {
		return errors.New("thiết bị không thuộc nhà này")
	}

	return ds.ControlDevice(userID, deviceID, command, value)
}

func (s *DeviceService) GetRealTimeDeviceStatusInHome(userID int, homeID int, deviceID int) (*models.RealTimeDeviceStatus, error) {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return nil, errors.New("không tìm thấy thiết bị")
	}
	if device.HomeID == nil || *device.HomeID != homeID {
		return nil, errors.New("thiết bị không thuộc nhà này")
	}

	return s.deviceRepo.GetRealTimeDeviceStatus(deviceID)
}

func (s *DeviceService) GetAllRealTimeDeviceStatusInHome(userID int, homeID int) ([]models.RealTimeDeviceStatus, error) {
	hasPermission, err := s.permissionRepo.CheckHomeViewPermission(userID, homeID)
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New("không có quyền truy cập nhà này")
	}

	return s.deviceRepo.GetAllRealTimeDeviceStatusByHome(homeID)
}

func (s *DeviceService) BatchControlDevicesInHome(userID int, homeID int, req *models.BatchControlRequest) (*models.BatchControlResponse, error) {
	var commands []models.DeviceCommand
	var results []models.BatchControlResult

	for _, cmd := range req.DeviceCommands {
		device, err := s.deviceRepo.GetDeviceByID(cmd.DeviceID)
		if err != nil {
			results = append(results, models.BatchControlResult{
				DeviceID: cmd.DeviceID,
				Success:  false,
				Error:    "Không tìm thấy thiết bị",
			})
			continue
		}
		if device.HomeID == nil || *device.HomeID != homeID {
			results = append(results, models.BatchControlResult{
				DeviceID: cmd.DeviceID,
				Success:  false,
				Error:    "Thiết bị không thuộc nhà này",
			})
			continue
		}

		isAdmin, err := s.permissionRepo.CheckHomeAdminPermission(userID, homeID)
		if err != nil {
			results = append(results, models.BatchControlResult{
				DeviceID: cmd.DeviceID,
				Success:  false,
				Error:    "Lỗi kiểm tra quyền",
			})
			continue
		}
		if !isAdmin {
			hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, cmd.DeviceID)
			if err != nil || !hasPermission {
				results = append(results, models.BatchControlResult{
					DeviceID: cmd.DeviceID,
					Success:  false,
					Error:    "Không có quyền điều khiển thiết bị này",
				})
				continue
			}
		}

		command := models.DeviceCommand{
			DeviceID:  cmd.DeviceID,
			UserID:    userID,
			Command:   cmd.Command,
			Value:     cmd.Value,
			Status:    "pending",
			CreatedAt: time.Now(),
		}
		commands = append(commands, command)
	}

	if req.ExecuteSequentially {
		for _, command := range commands {
			err := s.ControlDevice(userID, command.DeviceID, command.Command, command.Value)
			result := models.BatchControlResult{
				DeviceID: command.DeviceID,
				Success:  err == nil,
			}
			if err != nil {
				result.Error = err.Error()
			} else {
				result.Message = "Thành công"
			}
			results = append(results, result)
		}
	} else {
		batchResults, err := s.deviceRepo.ExecuteBatchControl(commands)
		if err != nil {
			return nil, err
		}
		results = append(results, batchResults...)
	}

	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	response := &models.BatchControlResponse{
		Status:  "completed",
		Message: fmt.Sprintf("Đã thực hiện %d/%d lệnh thành công", successCount, len(results)),
		Results: results,
	}

	return response, nil
}

func (s *DeviceService) UpdateDevicePropertyInHome(userID int, homeID int, deviceID int, propertyName string, value interface{}) error {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("không tìm thấy thiết bị")
	}
	if device.HomeID == nil || *device.HomeID != homeID {
		return errors.New("thiết bị không thuộc nhà này")
	}

	isAdmin, err := s.permissionRepo.CheckHomeAdminPermission(userID, homeID)
	if err != nil {
		return err
	}
	if !isAdmin {
		hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, deviceID)
		if err != nil {
			return err
		}
		if !hasPermission {
			return errors.New("không có quyền điều khiển thiết bị này")
		}
	}

	err = s.deviceRepo.UpdateDeviceProperty(deviceID, propertyName, value)
	if err != nil {
		return err
	}

	return nil
}

func (s *DeviceService) MonitorDeviceStatusInHome(userID int, homeID int, deviceID int) error {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("không tìm thấy thiết bị")
	}
	if device.HomeID == nil || *device.HomeID != homeID {
		return errors.New("thiết bị không thuộc nhà này")
	}

	return s.MonitorDeviceStatus(deviceID)
}

func (s *DeviceService) GetDeviceByIDInHome(userID int, homeID int, deviceID int) (*models.Device, error) {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return nil, errors.New("không tìm thấy thiết bị")
	}

	return device, nil
}

func (s *DeviceService) UpdateDeviceInHome(userID int, homeID int, deviceID int, req *models.UpdateDeviceRequest) error {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("không tìm thấy thiết bị")
	}
	if device.HomeID == nil || *device.HomeID != homeID {
		return errors.New("thiết bị không thuộc nhà này")
	}

	hasPermission, err := s.permissionRepo.CheckDeviceConfigPermission(userID, deviceID)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("không có quyền cấu hình thiết bị này")
	}

	if req.Name != "" {
		device.Name = req.Name
	}
	if req.AreaID != nil {
		if *req.AreaID == 0 {
			device.AreaID = nil
		} else {
			if !s.deviceRepo.AreaExistsInHome(*req.AreaID, homeID) {
				return errors.New("khu vực mới không tồn tại hoặc không thuộc nhà này")
			}
			device.AreaID = req.AreaID
		}
	}

	device.UpdatedAt = time.Now()

	return s.deviceRepo.UpdateDevice(device)
}

func (s *DeviceService) DeleteDeviceInHome(userID int, homeID int, deviceID int) error {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return errors.New("không tìm thấy thiết bị")
	}

	if device.HomeID == nil || *device.HomeID != homeID {
		return errors.New("thiết bị không thuộc nhà này")
	}

	hasPermission, err := s.permissionRepo.CheckDeviceConfigPermission(userID, deviceID)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("không có quyền xóa thiết bị này")
	}

	return s.deviceRepo.DeleteDevice(deviceID)
}

func (s *DeviceService) GetDevicePropertiesInHome(userID int, homeID int, deviceID int) ([]models.DeviceProperty, error) {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return nil, errors.New("không tìm thấy thiết bị")
	}
	if device.HomeID == nil || *device.HomeID != homeID {
		return nil, errors.New("thiết bị không thuộc nhà này")
	}

	return s.deviceRepo.GetDeviceProperties(deviceID)
}

func (s *DeviceService) GetDeviceCommandsInHome(userID int, homeID int, deviceID int) ([]models.DeviceCommand, error) {
	log.Printf("=== GetDeviceCommandsInHome Debug ===")
	log.Printf("UserID: %d, HomeID: %d, DeviceID: %d", userID, homeID, deviceID)

	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		log.Printf("Error getting device: %v", err)
		return nil, errors.New("không tìm thấy thiết bị")
	}
	log.Printf("Device found: ID=%d, HomeID=%v", device.DeviceID, device.HomeID)

	if device.HomeID == nil || *device.HomeID != homeID {
		log.Printf("Device home mismatch: device.HomeID=%v, expected=%d", device.HomeID, homeID)
		return nil, errors.New("thiết bị không thuộc nhà này")
	}

	hasPermission, err := s.permissionRepo.CheckDeviceControlPermission(userID, deviceID)
	if err != nil {
		log.Printf("Error checking permission: %v", err)
		return nil, err
	}
	log.Printf("Permission check result: %v", hasPermission)
	if !hasPermission {
		return nil, errors.New("không có quyền điều khiển thiết bị này")
	}

	log.Printf("Getting commands from repository...")
	commands, err := s.deviceRepo.GetDeviceCommands(deviceID)
	if err != nil {
		log.Printf("Error getting commands: %v", err)
		return nil, fmt.Errorf("lỗi khi lấy lệnh điều khiển: %v", err)
	}

	log.Printf("Commands retrieved: %d items", len(commands))
	if len(commands) == 0 {
		return []models.DeviceCommand{}, nil
	}

	return commands, nil
}

func (s *DeviceService) GetDeviceHistoryInHome(userID int, homeID int, deviceID int, limit int) ([]models.DeviceCommand, error) {
	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		return nil, errors.New("không tìm thấy thiết bị")
	}
	if device.HomeID == nil || *device.HomeID != homeID {
		return nil, errors.New("thiết bị không thuộc nhà này")
	}

	return s.deviceRepo.GetDeviceCommandHistory(deviceID, limit)
}

func (s *DeviceService) AddDeviceToAreaInHome(userID int, homeID int, deviceID int, areaID int) error {
	fmt.Printf(" AddDeviceToAreaInHome: userID=%d, homeID=%d, deviceID=%d, areaID=%d\n", userID, homeID, deviceID, areaID)

	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		fmt.Printf(" Device not found: %v\n", err)
		return errors.New("không tìm thấy thiết bị")
	}
	fmt.Printf(" Device found: ID=%d, HomeID=%v, CurrentAreaID=%v\n", device.DeviceID, device.HomeID, device.AreaID)

	if device.HomeID == nil || *device.HomeID != homeID {
		fmt.Printf(" Device doesn't belong to home: device.HomeID=%v, expected=%d\n", device.HomeID, homeID)
		return errors.New("thiết bị không thuộc nhà này")
	}

	if !s.deviceRepo.AreaExistsInHome(areaID, homeID) {
		fmt.Printf(" Area doesn't exist in home: areaID=%d, homeID=%d\n", areaID, homeID)
		return errors.New("khu vực không tồn tại hoặc không thuộc nhà này")
	}
	fmt.Printf(" Area exists in home: areaID=%d\n", areaID)

	hasPermission, err := s.permissionRepo.CheckDeviceConfigPermission(userID, deviceID)
	if err != nil {
		fmt.Printf(" Permission check error: %v\n", err)
		return err
	}
	if !hasPermission {
		fmt.Printf(" No permission to configure device: userID=%d, deviceID=%d\n", userID, deviceID)
		return errors.New("không có quyền cấu hình thiết bị này")
	}
	fmt.Printf(" Permission check passed\n")

	oldAreaID := device.AreaID
	fmt.Printf(" Updating device area: %v → %d\n", oldAreaID, areaID)

	err = s.deviceRepo.UpdateDeviceArea(deviceID, &areaID)
	if err != nil {
		fmt.Printf(" Failed to update device area: %v\n", err)
		return err
	}

	fmt.Printf(" Successfully added device %d to area %d\n", deviceID, areaID)
	return nil
}

func (s *DeviceService) RemoveDeviceFromAreaInHome(userID int, homeID int, deviceID int) error {
	fmt.Printf(" RemoveDeviceFromAreaInHome: userID=%d, homeID=%d, deviceID=%d\n", userID, homeID, deviceID)

	device, err := s.deviceRepo.GetDeviceByID(deviceID)
	if err != nil {
		fmt.Printf(" Device not found: %v\n", err)
		return errors.New("không tìm thấy thiết bị")
	}
	fmt.Printf(" Device found: ID=%d, HomeID=%v, CurrentAreaID=%v\n", device.DeviceID, device.HomeID, device.AreaID)

	if device.HomeID == nil || *device.HomeID != homeID {
		fmt.Printf(" Device doesn't belong to home: device.HomeID=%v, expected=%d\n", device.HomeID, homeID)
		return errors.New("thiết bị không thuộc nhà này")
	}

	hasPermission, err := s.permissionRepo.CheckDeviceConfigPermission(userID, deviceID)
	if err != nil {
		fmt.Printf(" Permission check error: %v\n", err)
		return err
	}
	if !hasPermission {
		fmt.Printf(" No permission to configure device: userID=%d, deviceID=%d\n", userID, deviceID)
		return errors.New("không có quyền cấu hình thiết bị này")
	}
	fmt.Printf(" Permission check passed\n")

	oldAreaID := device.AreaID
	fmt.Printf(" Removing device from area: %v → NULL\n", oldAreaID)

	err = s.deviceRepo.UpdateDeviceArea(deviceID, nil)
	if err != nil {
		fmt.Printf(" Failed to remove device from area: %v\n", err)
		return err
	}

	fmt.Printf(" Successfully removed device %d from area\n", deviceID)
	return nil
}

func (s *DeviceService) GetDevicesInAreaInHome(userID int, homeID int, areaID int) ([]models.Device, error) {
	if !s.deviceRepo.AreaExistsInHome(areaID, homeID) {
		return nil, errors.New("khu vực không tồn tại hoặc không thuộc nhà này")
	}

	isAdmin, err := s.permissionRepo.CheckHomeAdminPermission(userID, homeID)
	if err != nil {
		return nil, err
	}

	if !isAdmin {
		hasPermission, err := s.permissionRepo.CheckAreaViewPermission(userID, areaID)
		if err != nil {
			return nil, err
		}
		if !hasPermission {
			return nil, errors.New("không có quyền truy cập khu vực này")
		}
	}

	return s.deviceRepo.GetDevicesByAreaID(areaID)
}
