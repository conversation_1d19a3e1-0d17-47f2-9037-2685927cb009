package main

import (
	"dh52110724-api-quan-ly-nha-thong-minh/config"
	"dh52110724-api-quan-ly-nha-thong-minh/controller"
	"dh52110724-api-quan-ly-nha-thong-minh/database"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/automation"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/device"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/home"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/permission"
	"dh52110724-api-quan-ly-nha-thong-minh/repository/user"
	"dh52110724-api-quan-ly-nha-thong-minh/router"
	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/joho/godotenv"
)

func main() {
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}
	cfg := config.LoadConfig()

	if err := database.Connect(); err != nil {
		log.Fatalf("Database connection failed: %v", err)
	}

	userRepo := user.NewPostgresRepository(database.DB)
	permRepo := permission.NewPostgresRepository(database.DB)
	homeRepo := home.NewPostgresRepository(database.DB)
	deviceRepo := device.NewPostgresRepository(database.DB)
	automationRepo := automation.NewPostgresRepository(database.DB)

	log.Printf("Initializing MQTT service...")
	mqttConfig := service.MQTTConfig{
		Broker:   cfg.MQTTBroker,
		Port:     cfg.MQTTPort,
		ClientID: cfg.MQTTClientID,
		Username: cfg.MQTTUsername,
		Password: cfg.MQTTPassword,
	}
	log.Printf("MQTT Config: %s:%d (Client: %s)", cfg.MQTTBroker, cfg.MQTTPort, cfg.MQTTClientID)

	mqttService := service.NewMQTTService(mqttConfig, deviceRepo)

	if err := mqttService.SubscribeAllTopics(); err != nil {
		log.Printf("WARNING: MQTT subscription failed: %v", err)
	} else {
		log.Printf("MQTT service initialized successfully")
	}

	authService := service.NewAuthService(userRepo)
	permissionService := service.NewPermissionService(permRepo, userRepo)
	homeService := service.NewHomeService(homeRepo, userRepo, permRepo)
	deviceService := service.NewDeviceService(deviceRepo, permRepo)
	automationService := service.NewAutomationService(automationRepo, deviceRepo, deviceService, permRepo)

	authService.SetHomeService(homeService)
	deviceService.SetMQTTService(mqttService)

	authController := controller.NewAuthcontrollers(authService)
	permissionController := controller.NewPermissionController(permissionService, homeService)
	homeController := controller.NewHomeController(homeService)
	deviceController := controller.NewDeviceController(deviceService, homeService)
	deviceTypeController := controller.NewDeviceTypeController(deviceService)
	automationController := controller.NewAutomationController(automationService)
	invitationController := controller.NewInvitationController(homeService)

	r := router.SetupRouter(cfg, database.DB, authController, permissionController, homeController, deviceController, deviceTypeController, automationController, invitationController)

	log.Println("Starting Scheduler Service...")
	schedulerService := service.NewSchedulerService(automationRepo, automationService)
	schedulerService.Start()

	log.Println("MQTT Service is ready for real-time communication...")

	go func() {
		quit := make(chan os.Signal, 1)
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
		<-quit

		log.Println("Shutting down services...")

		schedulerService.Stop()

		log.Println("Services stopped")
		os.Exit(0)
	}()

	port := cfg.ServerPort
	if port == "" {
		port = "8080"
	}

	log.Printf("Smart Home API Server running on port %s", port)
	log.Printf("Monitoring Service: RUNNING")
	log.Printf("Scheduler Service: RUNNING")
	log.Printf("MQTT Service: RUNNING")
	log.Printf("Ready to execute automation rules!")

	if err := r.Run(":" + port); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}
