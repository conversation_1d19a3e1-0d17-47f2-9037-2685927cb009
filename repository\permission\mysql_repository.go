package permission

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type mysqlPermissionRepository  struct {
	db *gorm.DB
}

func NewPostgresRepository(db *gorm.DB) Repository {
	return &mysqlPermissionRepository {db: db}
}

func (r *mysqlPermissionRepository ) GetPermissionByID(permissionID int) (*models.Permission, error) {
	var permission models.Permission
	err := r.db.Where("permission_id = ?", permissionID).First(&permission).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("permission with ID %d not found", permissionID)
		}
		return nil, err
	}
	return &permission, nil
}

func (r *mysqlPermissionRepository ) CheckDevicePermission(userID int, deviceID int) (bool, error) {
	var count int64
	err := r.db.Model(&models.Permission{}).
		Where("user_id = ? AND device_id = ? AND can_control = ?", userID, deviceID, true).
		Count(&count).Error

	return count > 0, err
}

func (r *mysqlPermissionRepository ) CheckAreaPermission(userID int, areaID int) (bool, error) {
	var count int64
	err := r.db.Model(&models.Permission{}).
		Where("user_id = ? AND area_id = ? AND can_control = ?", userID, areaID, true).
		Count(&count).Error

	return count > 0, err
}

func (r *mysqlPermissionRepository ) GetUserPermissions(userID int) ([]models.Permission, error) {
	var permissions []models.Permission
	err := r.db.Where("user_id = ?", userID).
		Preload("Area").
		Preload("Device").
		Find(&permissions).Error

	return permissions, err
}

func (r *mysqlPermissionRepository ) AddDevicePermission(permission *models.Permission) error {
	return r.db.Create(permission).Error
}

func (r *mysqlPermissionRepository ) GetUserRoles(userID int) ([]models.Role, error) {
	var roles []models.Role
	err := r.db.Table("roles").
		Joins("JOIN user_roles ON roles.role_id = user_roles.role_id").
		Where("user_roles.user_id = ?", userID).
		Find(&roles).Error

	if err != nil {
		return nil, err
	}

	return roles, nil
}

func (r *mysqlPermissionRepository ) AddAreaPermission(permission *models.Permission) error {
	return r.db.Create(permission).Error
}

func (r *mysqlPermissionRepository ) RemoveDevicePermission(userID int, deviceID int) error {
	return r.db.Where("user_id = ? AND device_id = ?", userID, deviceID).
		Delete(&models.Permission{}).Error
}

func (r *mysqlPermissionRepository ) RemoveAreaPermission(userID int, areaID int) error {
	return r.db.Where("user_id = ? AND area_id = ?", userID, areaID).
		Delete(&models.Permission{}).Error
}

func (r *mysqlPermissionRepository ) UpdatePermission(permissionID int, canView, canControl bool) error {
	result := r.db.Model(&models.Permission{}).
		Where("permission_id = ?", permissionID).
		Updates(map[string]interface{}{
			"can_view":    canView,
			"can_control": canControl,
		})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("permission not found or no changes made")
	}

	return nil
}

func (r *mysqlPermissionRepository ) CheckDeviceControlPermission(userID int, deviceID int) (bool, error) {

	var count int64
	err := r.db.Debug().Model(&models.Permission{}).
		Where("user_id = ? AND device_id = ? AND can_control = ?", userID, deviceID, true).
		Count(&count).Error

	return count > 0, err
}

func (r *mysqlPermissionRepository ) CheckDeviceViewPermission(userID int, deviceID int) (bool, error) {
	var count int64
	err := r.db.Model(&models.Permission{}).
		Where("user_id = ? AND device_id = ? AND (can_view = ? OR can_control = ?)", userID, deviceID, true, true).
		Count(&count).Error

	return count > 0, err
}

func (r *mysqlPermissionRepository ) CheckDeviceConfigPermission(userID int, deviceID int) (bool, error) {
	var device models.Device
	err := r.db.Where("device_id = ?", deviceID).First(&device).Error
	if err != nil {
		return false, err
	}

	if device.HomeID == nil {
		return false, nil
	}

	var homeUser models.HomeUser
	err = r.db.Where("user_id = ? AND home_id = ? AND role_id IN (?)", userID, *device.HomeID, []int{1, 2}).First(&homeUser).Error
	if err == nil {
		return true, nil
	}

	var count int64
	err = r.db.Model(&models.Permission{}).
		Where("user_id = ? AND device_id = ? AND can_configure = ?", userID, deviceID, true).
		Count(&count).Error

	return count > 0, err
}

func (r *mysqlPermissionRepository ) CheckAreaViewPermission(userID int, areaID int) (bool, error) {
	var count int64
	err := r.db.Model(&models.Permission{}).
		Where("user_id = ? AND area_id = ? AND (can_view = ? OR can_control = ?)", userID, areaID, true, true).
		Count(&count).Error

	return count > 0, err
}
func (r *mysqlPermissionRepository ) GetPermissionByUserAndHome(userID, homeID int) (*models.Permission, error) {
	var permission models.Permission
	err := r.db.Where("user_id = ? AND home_id = ?", userID, homeID).First(&permission).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("permission not found")
		}
		return nil, err
	}
	return &permission, nil
}
func (r *mysqlPermissionRepository ) GetPermissionsByHome(homeID int) ([]models.Permission, error) {
	var permissions []models.Permission
	err := r.db.Table("permissions").
		Joins("JOIN devices ON permissions.device_id = devices.device_id").
		Joins("JOIN areas ON devices.area_id = areas.area_id").
		Where("areas.home_id = ?", homeID).
		Find(&permissions).Error

	return permissions, err
}

func (r *mysqlPermissionRepository ) GetPermissionsByHomeWithFilter(homeID int, filterType string) ([]models.Permission, error) {
	var permissions []models.Permission

	switch filterType {
	case "device":
		err := r.db.Preload("Device").Preload("Device.DeviceType").Preload("Area").
			Joins("JOIN devices ON permissions.device_id = devices.device_id").
			Where("devices.home_id = ? AND permissions.device_id IS NOT NULL", homeID).
			Find(&permissions).Error
		return permissions, err

	case "area":
		err := r.db.Preload("Area").
			Joins("JOIN areas ON permissions.area_id = areas.area_id").
			Where("areas.home_id = ? AND permissions.area_id IS NOT NULL", homeID).
			Find(&permissions).Error
		return permissions, err

	case "all":
		fallthrough
	default:
		var devicePermissions []models.Permission
		err := r.db.Preload("Device").Preload("Device.DeviceType").
			Joins("JOIN devices ON permissions.device_id = devices.device_id").
			Where("devices.home_id = ? AND permissions.device_id IS NOT NULL", homeID).
			Find(&devicePermissions).Error
		if err != nil {
			return nil, err
		}

		var areaPermissions []models.Permission
		err = r.db.Preload("Area").
			Joins("JOIN areas ON permissions.area_id = areas.area_id").
			Where("areas.home_id = ? AND permissions.area_id IS NOT NULL", homeID).
			Find(&areaPermissions).Error
		if err != nil {
			return nil, err
		}

		permissions = append(devicePermissions, areaPermissions...)
		return permissions, nil
	}
}

func (r *mysqlPermissionRepository ) CheckHomePermission(userID uint, homeID uint) error {
	var homeUser models.HomeUser

	result := r.db.Where("user_id = ? AND home_id = ?", userID, homeID).First(&homeUser)
	if result.Error != nil {
		return fmt.Errorf("user không có quyền truy cập home này")
	}

	if homeUser.RoleID != 1 && homeUser.RoleID != 2 {
		return fmt.Errorf("user không có đủ quyền hạn")
	}

	return nil
}

func (r *mysqlPermissionRepository ) CheckHomeViewPermission(userID int, homeID int) (bool, error) {
	var homeUser models.HomeUser

	err := r.db.Where("user_id = ? AND home_id = ?", userID, homeID).First(&homeUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, fmt.Errorf("lỗi truy vấn database: %v", err)
	}

	return true, nil
}

func (r *mysqlPermissionRepository ) GetUserRoleInHome(userID int, homeID int) (int, error) {
	var homeUser models.HomeUser

	err := r.db.Where("user_id = ? AND home_id = ?", userID, homeID).First(&homeUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, fmt.Errorf("user không thuộc home này")
		}
		return 0, fmt.Errorf("lỗi truy vấn database: %v", err)
	}

	return homeUser.RoleID, nil
}

func (r *mysqlPermissionRepository ) UpdateUserRoleInHome(userID int, homeID int, roleID int) error {
	if roleID < 1 || roleID > 3 {
		return fmt.Errorf("role ID không hợp lệ")
	}

	result := r.db.Model(&models.HomeUser{}).
		Where("user_id = ? AND home_id = ?", userID, homeID).
		Update("role_id", roleID)

	if result.Error != nil {
		return fmt.Errorf("lỗi cập nhật role: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("không tìm thấy user trong home")
	}

	return nil
}

func (r *mysqlPermissionRepository ) CheckUserCanKickUser(kickerID int, targetID int, homeID int) (bool, error) {
	kickerRole, err := r.GetUserRoleInHome(kickerID, homeID)
	if err != nil {
		return false, err
	}

	targetRole, err := r.GetUserRoleInHome(targetID, homeID)
	if err != nil {
		return false, err
	}


	if kickerID == targetID {
		return false, fmt.Errorf("không thể kick chính mình")
	}

	if targetRole == 1 {
		return false, fmt.Errorf("không thể kick OWNER")
	}

	if kickerRole == 1 && (targetRole == 2 || targetRole == 3) {
		return true, nil
	}

	if kickerRole == 2 && targetRole == 3 {
		return true, nil
	}

	return false, fmt.Errorf("không có quyền kick user này")
}

func (r *mysqlPermissionRepository ) CheckHomeAdminPermission(userID int, homeID int) (bool, error) {
	var homeUser models.HomeUser

	err := r.db.Where("user_id = ? AND home_id = ? AND role_id IN (?)", userID, homeID, []int{1, 2}).First(&homeUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, fmt.Errorf("lỗi truy vấn database: %v", err)
	}

	return true, nil
}
