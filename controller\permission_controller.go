package controller

import (
	"dh52110724-api-quan-ly-nha-thong-minh/middleware"
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PermissionController struct {
	permissionService *service.PermissionService
	homeService       *service.HomeService
}

func NewPermissionController(permissionService *service.PermissionService, homeService *service.HomeService) *PermissionController {
	return &PermissionController{
		permissionService: permissionService,
		homeService:       homeService,
	}
}

func (pc *PermissionController) GetPermissionsByHome(c *gin.Context) {
	homeID, err := strconv.Atoi(c.<PERSON>("home_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	filterType := c.Query("filter")
	if filterType == "" {
		filterType = "all"
	}

	if filterType != "device" && filterType != "area" && filterType != "all" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Filter type không hợp lệ. Chỉ chấp nhận: device, area, all")
		return
	}

	var permissions []models.Permission
	permissions, err = pc.permissionService.GetPermissionsByHomeWithFilter(homeID, filterType)

	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách quyền trong nhà thành công", map[string]interface{}{
		"filter":      filterType,
		"total":       len(permissions),
		"permissions": permissions,
	})
}

func (pc *PermissionController) GetAllPermissions(c *gin.Context) {
	homeID, err := strconv.Atoi(c.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	permissions, err := pc.permissionService.GetPermissionsByHomeWithFilter(homeID, "all")
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy tất cả quyền trong nhà thành công", map[string]interface{}{
		"total":       len(permissions),
		"permissions": permissions,
	})
}

func (pc *PermissionController) GetDevicePermissions(c *gin.Context) {
	homeID, err := strconv.Atoi(c.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	permissions, err := pc.permissionService.GetPermissionsByHomeWithFilter(homeID, "device")
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy quyền thiết bị trong nhà thành công", map[string]interface{}{
		"total":       len(permissions),
		"permissions": permissions,
	})
}

func (pc *PermissionController) GetAreaPermissions(c *gin.Context) {
	homeID, err := strconv.Atoi(c.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	permissions, err := pc.permissionService.GetPermissionsByHomeWithFilter(homeID, "area")
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy quyền khu vực trong nhà thành công", map[string]interface{}{
		"total":       len(permissions),
		"permissions": permissions,
	})
}

func (pc *PermissionController) GetUserPermissions(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
		return
	}

	permissions, err := pc.permissionService.GetUserPermissions(int(userID))
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi lấy thông tin quyền")
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách quyền thành công", permissions)
}

func (pc *PermissionController) AddDevicePermission(c *gin.Context) {
	var req struct {
		UserID     int  `json:"user_id" binding:"required"`
		DeviceID   int  `json:"device_id" binding:"required"`
		CanView    bool `json:"can_view"`
		CanControl bool `json:"can_control"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	permission := &models.Permission{
		UserID:   req.UserID,
		DeviceID: &req.DeviceID,
	}

	_ = req.CanView
	_ = req.CanControl

	if err := pc.permissionService.AddDevicePermission(permission); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Thêm quyền thiết bị thành công", nil)
}

func (pc *PermissionController) RemovePermission(c *gin.Context) {
	permissionID, err := strconv.Atoi(c.Param("permission_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quyền không hợp lệ")
		return
	}

	if err := pc.permissionService.RemovePermission(permissionID); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Xóa quyền thành công", nil)
}

func (pc *PermissionController) UpdatePermission(c *gin.Context) {
	permissionID, err := strconv.Atoi(c.Param("permission_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quyền không hợp lệ")
		return
	}

	var req struct {
		CanView    bool `json:"can_view"`
		CanControl bool `json:"can_control"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	if err := pc.permissionService.UpdatePermission(permissionID, req.CanView, req.CanControl); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Cập nhật quyền thành công", nil)
}

func (pc *PermissionController) AddAreaPermission(c *gin.Context) {
	_, err := strconv.Atoi(c.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var req struct {
		UserID     int  `json:"user_id" binding:"required"`
		AreaID     int  `json:"area_id" binding:"required"`
		CanView    bool `json:"can_view"`
		CanControl bool `json:"can_control"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	permission := &models.Permission{
		UserID: req.UserID,
		AreaID: &req.AreaID,
	}

	_ = req.CanView
	_ = req.CanControl

	if err := pc.permissionService.AddAreaPermission(permission); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Thêm quyền khu vực thành công", nil)
}
func (pc *PermissionController) ListUsers(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	homes, err := pc.homeService.GetUserHomes(userIDInt)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi lấy thông tin nhà")
		return
	}

	if len(homes) == 0 {
		utils.SuccessResponse(c, http.StatusOK, "Người dùng không thuộc nhà nào", []models.User{})
		return
	}

	homeID := homes[0].HomeID

	users, err := pc.homeService.GetHomeUsers(int(userID.(uint)), homeID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách người dùng")
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách người dùng thành công", users)
}

func (pc *PermissionController) AssignRole(c *gin.Context) {
	userID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID người dùng không hợp lệ")
		return
	}

	var req struct {
		RoleName string `json:"role_name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	if err := pc.permissionService.AssignUserRole(userID, req.RoleName); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Phân quyền thành công", nil)
}

func (pc *PermissionController) ListDevices(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	devices, err := pc.permissionService.GetUserDevices(userIDInt)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách thiết bị")
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách thiết bị thành công", devices)
}

func (pc *PermissionController) UpdateDevice(c *gin.Context) {
	deviceID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID thiết bị không hợp lệ")
		return
	}

	var req struct {
		PropertyID int    `json:"property_id" binding:"required"`
		Value      string `json:"value" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	userID, _ := c.Get("user_id")

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if err := pc.permissionService.UpdateUserDevice(userIDInt, deviceID, req.PropertyID, req.Value); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Cập nhật thiết bị thành công", nil)
}

func (pc *PermissionController) DeletePermission(c *gin.Context) {
	permissionID, err := strconv.Atoi(c.Param("permission_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID quyền không hợp lệ")
		return
	}

	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	homeID, err := strconv.Atoi(c.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	roleID := userRole.(int)

	if roleID == 1 {
		err = pc.permissionService.RemovePermission(permissionID)
	} else if roleID == 2 {
		err = pc.permissionService.RemovePermissionAsAdmin(userIDInt, homeID, permissionID)
	} else {
		utils.ErrorResponse(c, http.StatusForbidden, "Không có quyền xóa permission")
		return
	}

	if err != nil {
		if err.Error() == "permission not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy quyền")
			return
		}
		utils.ErrorResponse(c, http.StatusForbidden, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Xóa quyền thành công", nil)
}
