package models

type Permission struct {
	PermissionID int  `json:"permission_id" gorm:"primaryKey;column:permission_id"`
	UserID       int  `json:"user_id" gorm:"column:user_id"`
	AreaID       *int `json:"area_id" gorm:"column:area_id"`
	DeviceID     *int `json:"device_id" gorm:"column:device_id"`
	CanView      bool `json:"can_view" gorm:"column:can_view;default:true"`
	CanControl   bool `json:"can_control" gorm:"column:can_control;default:false"`
	CanConfigure bool `json:"can_configure" gorm:"column:can_configure;default:false"`

	Area   *Area   `json:"area,omitempty" gorm:"foreignKey:AreaID"`
	Device *Device `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
}

func (Permission) TableName() string {
	return "permissions"
}

type Role struct {
	RoleID int    `json:"role_id" gorm:"primaryKey;column:role_id"`
	Name   string `json:"name" gorm:"column:name;type:varchar(50);uniqueIndex"`
}

const (
	RoleOwner  = 1
	RoleAdmin  = 2
	RoleMember = 3
)

const (
	RoleOwnerName  = "OWNER"
	RoleAdminName  = "ADMIN"
	RoleMemberName = "MEMBER"
)

func (Role) TableName() string {
	return "roles"
}

type RoleDescription struct {
	RoleID      int    `json:"role_id" gorm:"primaryKey;column:role_id"`
	Description string `json:"description" gorm:"column:description;type:text"`
}

func (RoleDescription) TableName() string {
	return "role_descriptions"
}

type UserRole struct {
	UserID int `json:"user_id" gorm:"primaryKey;column:user_id"`
	RoleID int `json:"role_id" gorm:"primaryKey;column:role_id"`
}

func (UserRole) TableName() string {
	return "user_roles"
}
