package controller

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type HomeController struct {
	homeService *service.HomeService
}

func NewHomeController(homeService *service.HomeService) *HomeController {
	return &HomeController{
		homeService: homeService,
	}
}

func (c *HomeController) CreateHome(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	var req models.HomeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	homeID, err := c.homeService.CreateHome(int(userID.(uint)), &req)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "Tạo nhà thành công", map[string]interface{}{
		"home_id": homeID,
		"home":    req,
		"user_id": userID,
	})
}

func (c *HomeController) GetHome(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	home, err := c.homeService.GetHomeByID(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Lấy thông tin nhà thành công", home)
}

func (c *HomeController) GetHomeDetailWithStats(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	homeDetail, err := c.homeService.GetHomeDetailWithStats(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Lấy thông tin chi tiết nhà thành công", homeDetail)
}

func (c *HomeController) GetUserHomes(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	homes, err := c.homeService.GetUserHomes(userIDInt)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Lấy thông tin danh sách người nhà thành công", homes)
}

func (c *HomeController) UpdateHome(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var req models.HomeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	if err := c.homeService.UpdateHome(int(userID.(uint)), homeID, &req); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Cập nhật nhà thành công", nil)
}

func (c *HomeController) DeleteHome(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	if err := c.homeService.DeleteHome(int(userID.(uint)), homeID); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Xóa nhà thành công", nil)
}

func (c *HomeController) SendInvitation(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var req models.HomeInvitationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if err := c.homeService.SendInvitation(userIDInt, homeID, &req); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Gửi lời mời thành công", nil)
}

func (c *HomeController) ResendInvitation(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	invitationID, err := strconv.Atoi(ctx.Param("invitation_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID lời mời không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if err := c.homeService.ResendInvitation(userIDInt, homeID, invitationID); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Gửi lại lời mời thành công", nil)
}

func (c *HomeController) AddUserToHome(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var req models.HomeUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if err := c.homeService.AddUserToHome(userIDInt, homeID, &req); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Thêm người dùng vào nhà thành công", nil)
}

func (c *HomeController) RemoveUserFromHome(ctx *gin.Context) {
	adminID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	userID, err := strconv.Atoi(ctx.Param("user_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID người dùng không hợp lệ")
		return
	}

	var adminIDInt int
	switch v := adminID.(type) {
	case uint:
		adminIDInt = int(v)
	case int:
		adminIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if err := c.homeService.RemoveUserFromHome(adminIDInt, homeID, userID); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Xóa người dùng khỏi nhà thành công", nil)
}

func (c *HomeController) GetHomeUsers(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	users, err := c.homeService.GetHomeUsers(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Lấy danh sách người dùng thành công", users)
}

func (c *HomeController) GetHomeAreas(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	areas, err := c.homeService.GetHomeAreas(int(userID.(uint)), homeID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Lấy danh sách khu vực thành công", areas)
}

func (hc *HomeController) CreateArea(c *gin.Context) {
	homeID, _ := strconv.Atoi(c.Param("home_id"))
	var area models.Area
	if err := c.ShouldBindJSON(&area); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	area.HomeID = homeID
	_, err := hc.homeService.CreateArea(&area)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách khu vực thành công", area)
}

func (hc *HomeController) GetArea(c *gin.Context) {
	areaID, _ := strconv.Atoi(c.Param("area_id"))
	area, err := hc.homeService.GetAreaByID(areaID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Area not found"})
		return
	}

	c.JSON(http.StatusOK, area)
}

func (hc *HomeController) UpdateArea(c *gin.Context) {
	areaID, _ := strconv.Atoi(c.Param("area_id"))
	var area models.Area
	if err := c.ShouldBindJSON(&area); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	area.AreaID = areaID
	if err := hc.homeService.UpdateArea(&area); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Area updated successfully"})
}

func (hc *HomeController) DeleteArea(c *gin.Context) {
	areaID, _ := strconv.Atoi(c.Param("area_id"))
	if err := hc.homeService.DeleteArea(areaID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Area deleted successfully"})
}

func (c *HomeController) PromoteUser(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var req models.PromoteUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if err := c.homeService.PromoteUser(userIDInt, homeID, &req); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Thăng cấp user thành công", nil)
}

func (c *HomeController) TransferOwnership(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var req models.TransferOwnershipRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	if err := c.homeService.TransferOwnership(userIDInt, homeID, &req); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Chuyển quyền sở hữu thành công", nil)
}

func (c *HomeController) GetHomeUsersWithRoles(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	users, err := c.homeService.GetHomeUsersWithRoles(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Lấy danh sách user với roles thành công", users)
}

func (c *HomeController) AcceptInvitation(ctx *gin.Context) {
	token := ctx.Param("token")
	if token == "" {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Token không hợp lệ")
		return
	}

	if err := c.homeService.AcceptInvitation(token); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Chấp nhận lời mời thành công", nil)
}

func (c *HomeController) GetHomeInvitations(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	homeID, err := strconv.Atoi(ctx.Param("home_id"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	var userIDInt int
	switch v := userID.(type) {
	case uint:
		userIDInt = int(v)
	case int:
		userIDInt = v
	default:
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	invitations, err := c.homeService.GetInvitationsByHomeID(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Lấy danh sách lời mời thành công", invitations)
}
