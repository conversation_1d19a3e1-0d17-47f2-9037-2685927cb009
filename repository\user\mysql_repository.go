package user

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"errors"

	"gorm.io/gorm"
)

type mysqlUserRepository struct {
	db *gorm.DB
}

func NewPostgresRepository(db *gorm.DB) Repository {
	return &mysqlUserRepository{db: db}
}

func (r *mysqlUserRepository) CreateUser(user *models.User) error {
	if err := r.db.Create(user).Error; err != nil {
		return err
	}
	return nil
}

func (r *mysqlUserRepository) GetUserByUsername(username string) (*models.User, error) {
	var user models.User

	if err := r.db.Table("users").Where("email = ?", username).First(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *mysqlUserRepository) GetUserByEmail(email string) (*models.User, error) {
	var user models.User

	if err := r.db.Table("users").Where("email = ?", email).First(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *mysqlUserRepository) GetUserByID(id int) (*models.User, error) {
	var user models.User
	if err := r.db.Table("users").Where("user_id = ?", id).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *mysqlUserRepository) UpdateUser(user *models.User) error {
	return r.db.Save(user).Error
}

func (r *mysqlUserRepository) DeleteUser(userID int) error {
	return r.db.Delete(&models.User{}, userID).Error
}

func (r *mysqlUserRepository) CheckEmailExists(email string) (bool, error) {
	var count int64
	err := r.db.Table("users").Where("email = ?", email).Count(&count).Error
	return count > 0, err
}

func (r *mysqlUserRepository) CheckUsernameExists(username string) (bool, error) {
	var count int64
	err := r.db.Table("users").Where("email = ?", username).Count(&count).Error
	return count > 0, err
}

func (r *mysqlUserRepository) CheckPhoneExists(phone string) (bool, error) {
	var count int64
	err := r.db.Table("users").Where("phone_number = ?", phone).Count(&count).Error
	return count > 0, err
}

func (r *mysqlUserRepository) IsUserAdmin(userID int) (bool, error) {
	var count int64
	err := r.db.Table("user_roles").
		Joins("JOIN roles ON user_roles.role_id = roles.role_id").
		Where("user_roles.user_id = ? AND roles.name = ?", userID, "Administrator").
		Count(&count).Error

	return count > 0, err
}

func (r *mysqlUserRepository) AssignRole(userID int, roleName string) error {
	tx := r.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	var role models.Role
	if err := tx.Where("name = ?", roleName).First(&role).Error; err != nil {
		tx.Rollback()
		return errors.New("role không tồn tại")
	}

	userRole := models.UserRole{
		UserID: userID,
		RoleID: role.RoleID,
	}

	if err := tx.Create(&userRole).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

func (r *mysqlUserRepository) GetUserRoles(userID int) ([]models.Role, error) {
	var roles []models.Role
	err := r.db.Table("roles").
		Joins("JOIN user_roles ON roles.role_id = user_roles.role_id").
		Where("user_roles.user_id = ?", userID).
		Find(&roles).Error
	return roles, err
}

func (r *mysqlUserRepository) UpdateDeviceState(deviceState *models.DeviceState) error {
	tx := r.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	var existingDevice models.Device
	if err := tx.First(&existingDevice, deviceState.DeviceID).Error; err != nil {
		tx.Rollback()
		return errors.New("thiết bị không tồn tại")
	}

	var existingProperty models.DeviceProperty
	if err := tx.First(&existingProperty, deviceState.PropertyID).Error; err != nil {
		tx.Rollback()
		return errors.New("thuộc tính thiết bị không tồn tại")
	}

	var existingState models.DeviceState
	err := tx.Where("device_id = ? AND property_id = ?", deviceState.DeviceID, deviceState.PropertyID).
		First(&existingState).Error

	if err == gorm.ErrRecordNotFound {
		if err := tx.Create(deviceState).Error; err != nil {
			tx.Rollback()
			return err
		}
	} else if err != nil {
		tx.Rollback()
		return err
	} else {
		existingState.Value = deviceState.Value
		existingState.Timestamp = deviceState.Timestamp
		if err := tx.Save(&existingState).Error; err != nil {
			tx.Rollback()
			return err
		}
	}


	return tx.Commit().Error
}
