package models

import "time"

type AutomationRule struct {
	RuleID      int       `json:"rule_id" gorm:"primaryKey;column:rule_id;autoIncrement"`
	UserID      int       `json:"user_id" gorm:"column:user_id;not null"`
	HomeID      *int      `json:"home_id" gorm:"column:home_id"`
	TemplateID  *int      `json:"template_id" gorm:"column:template_id"`
	Name        string    `json:"name" gorm:"column:name;type:varchar(100);not null"`
	Description string    `json:"description" gorm:"column:description;type:text"`
	RuleType    string    `json:"rule_type" gorm:"column:rule_type;type:enum('schedule','condition','sensor');not null"`
	IsActive    bool      `json:"is_active" gorm:"column:is_active;default:true"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at;default:CURRENT_TIMESTAMP"`

	Conditions []RuleCondition `json:"conditions,omitempty" gorm:"foreignKey:RuleID"`
	Actions    []RuleAction    `json:"actions,omitempty" gorm:"foreignKey:RuleID"`
	Schedule   *RuleSchedule   `json:"schedule,omitempty" gorm:"foreignKey:RuleID"`
	Template   *RuleTemplate   `json:"template,omitempty" gorm:"foreignKey:TemplateID"`
}

func (AutomationRule) TableName() string {
	return "automation_rules"
}

type RuleTemplate struct {
	TemplateID       int       `json:"template_id" gorm:"primaryKey;column:template_id;autoIncrement"`
	Name             string    `json:"name" gorm:"column:name;type:varchar(100);not null"`
	Description      string    `json:"description" gorm:"column:description;type:text"`
	RuleType         string    `json:"rule_type" gorm:"column:rule_type;type:enum('schedule','condition');not null"`
	IsSystemTemplate bool      `json:"is_system_template" gorm:"column:is_system_template;default:false"`
	TemplateData     string    `json:"template_data" gorm:"column:template_data;type:json"`
	CreatedAt        time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
}

func (RuleTemplate) TableName() string {
	return "rule_templates"
}

type RuleTemplateDescription struct {
	TemplateID  int    `json:"template_id" gorm:"primaryKey;column:template_id"`
	Description string `json:"description" gorm:"column:description;type:text"`
}

func (RuleTemplateDescription) TableName() string {
	return "rule_template_descriptions"
}

type RuleCondition struct {
	ConditionID     int    `json:"condition_id" gorm:"primaryKey;column:condition_id;autoIncrement"`
	RuleID          int    `json:"rule_id" gorm:"column:rule_id;not null"`
	DeviceID        int    `json:"device_id" gorm:"column:device_id;not null"`
	PropertyID      *int   `json:"property_id" gorm:"column:property_id"`
	Operator        string `json:"operator" gorm:"column:operator;type:enum('<','<=','=','>=','>','!=');not null"`
	Value           string `json:"value" gorm:"column:value;type:text;not null"`
	LogicalOperator string `json:"logical_operator" gorm:"column:logical_operator;type:enum('AND','OR');default:'AND'"`

	Device   *Device         `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
	Property *DeviceProperty `json:"property,omitempty" gorm:"foreignKey:PropertyID"`
}

func (RuleCondition) TableName() string {
	return "automation_conditions"
}

type RuleAction struct {
	ActionID     int    `json:"action_id" gorm:"primaryKey;column:action_id;autoIncrement"`
	RuleID       int    `json:"rule_id" gorm:"column:rule_id;not null"`
	DeviceID     int    `json:"device_id" gorm:"column:device_id;not null"`
	PropertyID   int    `json:"property_id" gorm:"column:property_id;not null"`
	Value        string `json:"value" gorm:"column:value;type:text;not null"`
	DelaySeconds int    `json:"delay_seconds" gorm:"column:delay_seconds;default:0"`

	Device   *Device         `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
	Property *DeviceProperty `json:"property,omitempty" gorm:"foreignKey:PropertyID"`
}

func (RuleAction) TableName() string {
	return "automation_actions"
}

type RuleSchedule struct {
	RuleID             int        `json:"rule_id" gorm:"primaryKey;column:rule_id"`
	ScheduleExpression string     `json:"schedule_expression" gorm:"column:schedule_expression;type:varchar(100);not null"`
	Timezone           string     `json:"timezone" gorm:"column:timezone;type:varchar(50);default:'UTC'"`
	NextRunAt          *time.Time `json:"next_run_at" gorm:"column:next_run_at"`
	LastRunAt          *time.Time `json:"last_run_at" gorm:"column:last_run_at"`

	Rule *AutomationRule `json:"rule,omitempty" gorm:"foreignKey:RuleID"`
}

func (RuleSchedule) TableName() string {
	return "rule_schedules"
}

type CreateScheduleRuleRequest struct {
	Name               string `json:"name" binding:"required"`
	ScheduleExpression string `json:"schedule_expression" binding:"required"`
	Actions            []struct {
		DeviceID int    `json:"device_id" binding:"required"`
		Command  string `json:"command" binding:"required"`
		Value    string `json:"value"`
	} `json:"actions" binding:"required,dive"`
}

type CreateConditionRuleRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	HomeID      int    `json:"home_id" binding:"required"`
	IsActive    bool   `json:"is_active"`
	Conditions  []struct {
		DeviceID     int    `json:"device_id" binding:"required"`
		PropertyName string `json:"property_name"`
		Operator     string `json:"operator" binding:"required"`
		Value        string `json:"value" binding:"required"`
	} `json:"conditions" binding:"required,dive"`
	Actions []struct {
		DeviceID int    `json:"device_id" binding:"required"`
		Command  string `json:"command" binding:"required"`
		Value    string `json:"value"`
	} `json:"actions" binding:"required,dive"`
}

type RuleActionRequest struct {
	DeviceID     int    `json:"device_id" binding:"required"`
	PropertyID   int    `json:"property_id"`
	Command      string `json:"command"`
	Value        string `json:"value" binding:"required"`
	DelaySeconds int    `json:"delay_seconds"`
}

type RuleConditionRequest struct {
	DeviceID        int    `json:"device_id" binding:"required"`
	PropertyID      *int   `json:"property_id"`
	Operator        string `json:"operator" binding:"required"`
	Value           string `json:"value" binding:"required"`
	LogicalOperator string `json:"logical_operator"`
}

type RuleExecutionLog struct {
	LogID           int       `json:"id" gorm:"primaryKey;column:id;autoIncrement"`
	RuleID          int       `json:"rule_id" gorm:"column:rule_id;not null"`
	Status          string    `json:"status" gorm:"column:status;type:enum('success','failed','partial');not null"`
	Message         string    `json:"message" gorm:"column:message;type:text"`
	ExecutionTimeMs int       `json:"execution_time_ms" gorm:"column:execution_time_ms;default:0"`
	TriggeredBy     string    `json:"triggered_by" gorm:"column:triggered_by;type:varchar(50)"`
	CreatedAt       time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`

	Rule *AutomationRule `json:"rule,omitempty" gorm:"foreignKey:RuleID"`
}

func (RuleExecutionLog) TableName() string {
	return "rule_execution_logs"
}

type CreateAutomationRuleRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	RuleType    string                 `json:"rule_type" binding:"required,oneof=schedule condition sensor"`
	HomeID      int                    `json:"home_id,omitempty"`
	IsActive    bool                   `json:"is_active"`
	Conditions  []RuleConditionRequest `json:"conditions,omitempty"`
	Actions     []RuleActionRequest    `json:"actions" binding:"required,dive"`
	Schedule    *RuleScheduleRequest   `json:"schedule,omitempty"`
}

type RuleScheduleRequest struct {
	ScheduleExpression string `json:"schedule_expression"`
	Timezone           string `json:"timezone"`
	Time   string   `json:"time"`
	Days   []string `json:"days"`
	Repeat string   `json:"repeat"`
}

type UpdateAutomationRuleRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	IsActive    *bool                  `json:"is_active"`
	Conditions  []RuleConditionRequest `json:"conditions,omitempty"`
	Actions     []RuleActionRequest    `json:"actions,omitempty"`
	Schedule    *RuleScheduleRequest   `json:"schedule,omitempty"`
}

type AutomationRuleResponse struct {
	RuleID      int             `json:"rule_id"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	RuleType    string          `json:"rule_type"`
	IsActive    bool            `json:"is_active"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	Conditions  []RuleCondition `json:"conditions,omitempty"`
	Actions     []RuleAction    `json:"actions,omitempty"`
	Schedule    *RuleSchedule   `json:"schedule,omitempty"`
	Template    *RuleTemplate   `json:"template,omitempty"`
}

type RuleExecutionResponse struct {
	LogID           int       `json:"log_id"`
	RuleID          int       `json:"rule_id"`
	RuleName        string    `json:"rule_name"`
	Status          string    `json:"status"`
	Message         string    `json:"message"`
	ExecutionTimeMs int       `json:"execution_time_ms"`
	TriggeredBy     string    `json:"triggered_by"`
	CreatedAt       time.Time `json:"created_at"`
}

type RuleTemplateResponse struct {
	TemplateID       int       `json:"template_id"`
	Name             string    `json:"name"`
	Description      string    `json:"description"`
	RuleType         string    `json:"rule_type"`
	IsSystemTemplate bool      `json:"is_system_template"`
	TemplateData     string    `json:"template_data"`
	CreatedAt        time.Time `json:"created_at"`
}
