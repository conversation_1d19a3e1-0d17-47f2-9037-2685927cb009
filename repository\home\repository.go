package home

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"time"
)

type Repository interface {
	CreateHome(home *models.Home) (int, error)
	GetHomeByID(homeID int) (*models.Home, error)
	GetHomesByUserID(userID int) ([]models.Home, error)
	UpdateHome(home *models.Home) error
	DeleteHome(homeID int) error

	AddUserToHome(homeUser *models.HomeUser) error
	RemoveUserFromHome(homeID int, userID int) error
	GetHomeUsers(homeID int) ([]models.User, error)
	IsUserInHome(homeID int, userID int) (bool, error)
	IsHomeOwner(homeID int, userID int) (bool, error)
	GetUserHome(userID int) (*models.Home, error)
	IsUserOwner(homeID int, userID int) (bool, error)
	GetHomeMemberCount(homeID int) (int, error)

	GetHomeStatistics(homeID int) (*models.HomeStatistics, error)
	GetDeviceCountByType(homeID int) ([]models.DeviceTypeCount, error)
	GetDeviceCountByArea(homeID int) ([]models.AreaDeviceCount, error)
	GetRecentActivities(homeID int, limit int) ([]models.RecentActivity, error)

	GetHomeAreas(homeID int) ([]models.Area, error)
	CreateArea(area *models.Area) (int, error)
	UpdateArea(area *models.Area) error
	DeleteArea(areaID int) error
	GetAreaByID(areaID int) (*models.Area, error)

	CreateInvitation(invitation *models.HomeInvitation) error
	GetInvitationByToken(token string) (*models.HomeInvitation, error)
	GetInvitationsByHomeID(homeID int) ([]models.HomeInvitation, error)
	GetInvitationsByEmail(email string) ([]models.HomeInvitation, error)
	UpdateInvitationStatus(invitationID int, status string) error
	UpdateInvitationToken(invitationID int, token string, expiresAt time.Time) error
	DeleteInvitation(invitationID int) error
	DeleteExpiredInvitations() error
	CheckExistingInvitation(homeID int, email string) (bool, error)
}
