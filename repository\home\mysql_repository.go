package home

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type mysqlHomeRepository struct {
	db *gorm.DB
}

func NewPostgresRepository(db *gorm.DB) Repository {
	return &mysqlHomeRepository{db: db}
}

func (r *mysqlHomeRepository) CreateHome(home *models.Home) (int, error) {
	if err := r.db.Create(home).Error; err != nil {
		return 0, err
	}
	return home.HomeID, nil
}

func (r *mysqlHomeRepository) GetHomeByID(homeID int) (*models.Home, error) {
	var home models.Home
	err := r.db.Where("home_id = ?", homeID).First(&home).Error
	if err != nil {
		return nil, err
	}
	return &home, nil
}

func (r *mysqlHomeRepository) UpdateHome(home *models.Home) error {
	return r.db.Save(home).Error
}

func (r *mysqlHomeRepository) DeleteHome(homeID int) error {
	return r.db.Delete(&models.Home{}, homeID).Error
}

func (r *mysqlHomeRepository) GetUserHomes(userID int) ([]models.Home, error) {
	var homes []models.Home
	err := r.db.Joins("JOIN home_users ON homes.home_id = home_users.home_id").
		Where("home_users.user_id = ?", userID).
		Find(&homes).Error
	return homes, err
}

func (r *mysqlHomeRepository) GetHomesByUserID(userID int) ([]models.Home, error) {
	return r.GetUserHomes(userID)
}

func (r *mysqlHomeRepository) CreateArea(area *models.Area) (int, error) {
	if err := r.db.Create(area).Error; err != nil {
		return 0, err
	}
	return area.AreaID, nil
}

func (r *mysqlHomeRepository) GetAreasByHomeID(homeID int) ([]models.Area, error) {
	var areas []models.Area
	err := r.db.Where("home_id = ?", homeID).Find(&areas).Error
	return areas, err
}

func (r *mysqlHomeRepository) GetAreaByID(areaID int) (*models.Area, error) {
	var area models.Area
	err := r.db.Where("area_id = ?", areaID).First(&area).Error
	if err != nil {
		return nil, err
	}
	return &area, nil
}

func (r *mysqlHomeRepository) UpdateArea(area *models.Area) error {
	return r.db.Save(area).Error
}

func (r *mysqlHomeRepository) DeleteArea(areaID int) error {
	return r.db.Delete(&models.Area{}, areaID).Error
}

func (r *mysqlHomeRepository) GetHomeAreas(homeID int) ([]models.Area, error) {
	var areas []models.Area
	err := r.db.Where("home_id = ?", homeID).Find(&areas).Error
	return areas, err
}

func (r *mysqlHomeRepository) GetHomeStatistics(homeID int) (*models.HomeStatistics, error) {
	stats := &models.HomeStatistics{}

	var areaCount int64
	if err := r.db.Model(&models.Area{}).Where("home_id = ?", homeID).Count(&areaCount).Error; err != nil {
		return nil, err
	}
	stats.TotalAreas = int(areaCount)

	var totalDevices, onlineDevices int64
	if err := r.db.Model(&models.Device{}).
		Joins("LEFT JOIN areas ON devices.area_id = areas.area_id").
		Where("areas.home_id = ? OR (devices.area_id IS NULL AND devices.home_id = ?)", homeID, homeID).
		Count(&totalDevices).Error; err != nil {
		return nil, err
	}

	if err := r.db.Model(&models.Device{}).
		Joins("LEFT JOIN areas ON devices.area_id = areas.area_id").
		Where("(areas.home_id = ? OR (devices.area_id IS NULL AND devices.home_id = ?)) AND devices.is_online = ?", homeID, homeID, true).
		Count(&onlineDevices).Error; err != nil {
		return nil, err
	}

	stats.TotalDevices = int(totalDevices)
	stats.OnlineDevices = int(onlineDevices)
	stats.OfflineDevices = int(totalDevices - onlineDevices)

	var userCount int64
	if err := r.db.Model(&models.HomeUser{}).Where("home_id = ?", homeID).Count(&userCount).Error; err != nil {
		return nil, err
	}
	stats.TotalUsers = int(userCount)

	devicesByType, err := r.GetDeviceCountByType(homeID)
	if err != nil {
		return nil, err
	}
	stats.DevicesByType = devicesByType

	devicesByArea, err := r.GetDeviceCountByArea(homeID)
	if err != nil {
		return nil, err
	}
	stats.DevicesByArea = devicesByArea

	recentActivities, err := r.GetRecentActivities(homeID, 10)
	if err != nil {
		return nil, err
	}
	stats.RecentActivities = recentActivities

	return stats, nil
}

func (r *mysqlHomeRepository) GetDevicesByHomeID(homeID int) ([]models.Device, error) {
	var devices []models.Device

	err := r.db.Preload("DeviceType").Preload("Area").
		Joins("LEFT JOIN areas ON devices.area_id = areas.area_id").
		Where("areas.home_id = ? OR (devices.area_id IS NULL AND devices.home_id = ?)", homeID, homeID).
		Find(&devices).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get devices by home ID %d: %w", homeID, err)
	}

	return devices, nil
}

func (r *mysqlHomeRepository) AddUserToHome(homeUser *models.HomeUser) error {
	return r.db.Create(homeUser).Error
}

func (r *mysqlHomeRepository) RemoveUserFromHome(homeID int, userID int) error {
	return r.db.Where("home_id = ? AND user_id = ?", homeID, userID).Delete(&models.HomeUser{}).Error
}

func (r *mysqlHomeRepository) GetHomeUsers(homeID int) ([]models.User, error) {
	var users []models.User
	err := r.db.Joins("JOIN home_users ON users.user_id = home_users.user_id").
		Where("home_users.home_id = ?", homeID).
		Find(&users).Error
	return users, err
}

func (r *mysqlHomeRepository) IsUserInHome(homeID int, userID int) (bool, error) {
	var count int64
	err := r.db.Model(&models.HomeUser{}).
		Where("home_id = ? AND user_id = ?", homeID, userID).
		Count(&count).Error
	return count > 0, err
}

func (r *mysqlHomeRepository) IsHomeOwner(homeID int, userID int) (bool, error) {
	var count int64
	err := r.db.Model(&models.HomeUser{}).
		Joins("JOIN roles ON home_users.role_id = roles.role_id").
		Where("home_users.home_id = ? AND home_users.user_id = ? AND roles.name = ?", homeID, userID, "OWNER").
		Count(&count).Error
	return count > 0, err
}

func (r *mysqlHomeRepository) GetUserRoleInHome(userID int, homeID int) (int, error) {
	var homeUser models.HomeUser
	err := r.db.Where("user_id = ? AND home_id = ?", userID, homeID).First(&homeUser).Error
	if err != nil {
		return 0, err
	}
	return homeUser.RoleID, nil
}

func (r *mysqlHomeRepository) UpdateUserRole(homeID int, userID int, roleID int) error {
	return r.db.Model(&models.HomeUser{}).
		Where("home_id = ? AND user_id = ?", homeID, userID).
		Update("role_id", roleID).Error
}

func (r *mysqlHomeRepository) TransferOwnership(homeID int, currentOwnerID int, newOwnerID int) error {
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Model(&models.HomeUser{}).
		Where("home_id = ? AND user_id = ?", homeID, currentOwnerID).
		Update("role_id", 2).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&models.HomeUser{}).
		Where("home_id = ? AND user_id = ?", homeID, newOwnerID).
		Update("role_id", 1).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

func (r *mysqlHomeRepository) GetDeviceCountByType(homeID int) ([]models.DeviceTypeCount, error) {
	var results []models.DeviceTypeCount

	query := `
		SELECT
			dt.device_type_id,
			dt.name as device_type_name,
			COUNT(d.device_id) as total_count,
			COUNT(CASE WHEN d.is_online = true THEN 1 END) as online_count
		FROM device_types dt
		LEFT JOIN devices d ON dt.device_type_id = d.device_type_id
		LEFT JOIN areas a ON d.area_id = a.area_id
		WHERE a.home_id = ? OR (d.area_id IS NULL AND d.home_id = ?)
		GROUP BY dt.device_type_id, dt.name
		HAVING total_count > 0
		ORDER BY total_count DESC
	`

	if err := r.db.Raw(query, homeID, homeID).Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *mysqlHomeRepository) GetDeviceCountByArea(homeID int) ([]models.AreaDeviceCount, error) {
	var results []models.AreaDeviceCount

	query := `
		SELECT
			a.area_id,
			a.name as area_name,
			COUNT(d.device_id) as total_count,
			COUNT(CASE WHEN d.is_online = true THEN 1 END) as online_count
		FROM areas a
		LEFT JOIN devices d ON a.area_id = d.area_id
		WHERE a.home_id = ?
		GROUP BY a.area_id, a.name
		ORDER BY total_count DESC
	`

	if err := r.db.Raw(query, homeID).Scan(&results).Error; err != nil {
		return nil, err
	}

	var unassignedCount models.AreaDeviceCount
	unassignedQuery := `
		SELECT
			0 as area_id,
			'Chưa phân khu vực' as area_name,
			COUNT(d.device_id) as total_count,
			COUNT(CASE WHEN d.is_online = true THEN 1 END) as online_count
		FROM devices d
		WHERE d.home_id = ? AND d.area_id IS NULL
	`

	if err := r.db.Raw(unassignedQuery, homeID).Scan(&unassignedCount).Error; err != nil {
		return nil, err
	}

	if unassignedCount.TotalCount > 0 {
		results = append(results, unassignedCount)
	}

	return results, nil
}

func (r *mysqlHomeRepository) CreateInvitation(invitation *models.HomeInvitation) error {
	return r.db.Create(invitation).Error
}

func (r *mysqlHomeRepository) GetInvitationByToken(token string) (*models.HomeInvitation, error) {
	var invitation models.HomeInvitation
	err := r.db.Where("token = ?", token).First(&invitation).Error
	if err != nil {
		return nil, err
	}
	return &invitation, nil
}

func (r *mysqlHomeRepository) GetInvitationsByHomeID(homeID int) ([]models.HomeInvitation, error) {
	var invitations []models.HomeInvitation
	err := r.db.Where("home_id = ?", homeID).Find(&invitations).Error
	return invitations, err
}

func (r *mysqlHomeRepository) GetInvitationsByEmail(email string) ([]models.HomeInvitation, error) {
	var invitations []models.HomeInvitation
	err := r.db.Where("email = ?", email).Find(&invitations).Error
	return invitations, err
}

func (r *mysqlHomeRepository) UpdateInvitationStatus(invitationID int, status string) error {
	updates := map[string]interface{}{
		"status": status,
	}

	if status == "accepted" {
		updates["accepted_at"] = "NOW()"
	}

	return r.db.Model(&models.HomeInvitation{}).
		Where("invitation_id = ?", invitationID).
		Updates(updates).Error
}

func (r *mysqlHomeRepository) UpdateInvitationToken(invitationID int, token string, expiresAt time.Time) error {
	updates := map[string]interface{}{
		"token":      token,
		"expires_at": expiresAt,
		"created_at": time.Now(),
	}

	return r.db.Model(&models.HomeInvitation{}).
		Where("invitation_id = ?", invitationID).
		Updates(updates).Error
}

func (r *mysqlHomeRepository) DeleteInvitation(invitationID int) error {
	return r.db.Delete(&models.HomeInvitation{}, invitationID).Error
}

func (r *mysqlHomeRepository) CheckExistingInvitation(homeID int, email string) (bool, error) {
	var count int64
	err := r.db.Model(&models.HomeInvitation{}).
		Where("home_id = ? AND email = ? AND status = ?", homeID, email, "pending").
		Count(&count).Error
	return count > 0, err
}

func (r *mysqlHomeRepository) GetUserHome(userID int) (*models.Home, error) {
	var home models.Home
	err := r.db.Joins("JOIN home_users ON homes.home_id = home_users.home_id").
		Where("home_users.user_id = ?", userID).
		First(&home).Error
	if err != nil {
		return nil, err
	}
	return &home, nil
}

func (r *mysqlHomeRepository) IsUserOwner(homeID int, userID int) (bool, error) {
	var count int64
	err := r.db.Model(&models.HomeUser{}).
		Joins("JOIN roles ON home_users.role_id = roles.role_id").
		Where("home_users.home_id = ? AND home_users.user_id = ? AND roles.name = ?", homeID, userID, "OWNER").
		Count(&count).Error
	return count > 0, err
}

func (r *mysqlHomeRepository) GetHomeMemberCount(homeID int) (int, error) {
	var count int64
	err := r.db.Model(&models.HomeUser{}).
		Where("home_id = ?", homeID).
		Count(&count).Error
	return int(count), err
}

func (r *mysqlHomeRepository) DeleteExpiredInvitations() error {
	return r.db.Where("expires_at < ? AND status = ?", time.Now(), "pending").
		Delete(&models.HomeInvitation{}).Error
}

func (r *mysqlHomeRepository) GetRecentActivities(homeID int, limit int) ([]models.RecentActivity, error) {
	return []models.RecentActivity{}, nil
}
