package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"

	"github.com/gin-gonic/gin"
)

type DeviceController struct {
	deviceService *service.DeviceService
	homeService   *service.HomeService
}

func NewDeviceController(deviceService *service.DeviceService, homeService *service.HomeService) *DeviceController {
	return &DeviceController{
		deviceService: deviceService,
		homeService:   homeService,
	}
}

func getUserIDAsInt(userID interface{}) (int, error) {
	switch v := userID.(type) {
	case uint:
		return int(v), nil
	case int:
		return v, nil
	case float64:
		return int(v), nil
	default:
		return 0, fmt.<PERSON>rrorf("invalid user ID type: %T", userID)
	}
}

func (dc *DeviceController) checkHomeAccess(c *gin.Context) (int, int, bool) {
	homeIDStr := c.Param("home_id")
	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return 0, 0, false
	}

	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return 0, 0, false
	}

	if dc.homeService != nil {
		_, err := dc.homeService.GetHomeByID(userIDInt, homeID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusForbidden, "Bạn không có quyền truy cập nhà này")
			return 0, 0, false
		}
	}

	return userIDInt, homeID, true
}

func (dc *DeviceController) AddDeviceToHome(c *gin.Context) {
	homeIDStr := c.Param("home_id")
	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	var req models.AddDeviceToHomeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	device, err := dc.deviceService.AddDeviceToHome(&req, homeID, userIDInt)
	if err != nil {
		switch err.Error() {
		case "thiết bị đã được đăng ký trong hệ thống":
			utils.ErrorResponse(c, http.StatusConflict, "Thiết bị với unique_identifier này đã tồn tại trong hệ thống")
		case "thiết bị đã tồn tại nhưng chưa được thêm vào nhà nào, vui lòng liên hệ admin":
			utils.ErrorResponse(c, http.StatusConflict, "Thiết bị đã tồn tại nhưng chưa được thêm vào nhà nào, vui lòng liên hệ admin")
		case "khu vực không tồn tại hoặc không thuộc nhà này":
			utils.ErrorResponse(c, http.StatusBadRequest, "Khu vực không tồn tại hoặc không thuộc nhà này")
		case "loại thiết bị không tồn tại":
			utils.ErrorResponse(c, http.StatusBadRequest, "Loại thiết bị không tồn tại")
		default:
			utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi hệ thống: "+err.Error())
		}
		return
	}

	utils.SuccessResponse(c, http.StatusCreated, "Thêm thiết bị thành công", device)
}

func (dc *DeviceController) GetAvailableDevices(c *gin.Context) {
	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	devices, err := dc.deviceService.GetAvailableDevices(userIDInt)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách thiết bị: "+err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách thiết bị thành công", devices)
}

func (dc *DeviceController) RemoveDeviceFromHome(c *gin.Context) {
	fmt.Println(" RemoveDeviceFromHome called!")

	homeIDStr := c.Param("home_id")
	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID nhà không hợp lệ")
		return
	}

	deviceIDStr := c.Param("device_id")
	deviceID, err := strconv.Atoi(deviceIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID thiết bị không hợp lệ")
		return
	}

	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	err = dc.deviceService.DeleteDeviceInHome(userIDInt, homeID, deviceID)
	if err != nil {
		if err.Error() == "không tìm thấy thiết bị" || err.Error() == "record not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Thiết bị không tồn tại")
		} else {
			utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		}
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Xóa thiết bị hoàn toàn thành công", gin.H{"device_id": deviceID})
}

func (dc *DeviceController) ControlDevice(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))

	var req struct {
		Command string `json:"command" binding:"required"`
		Value   string `json:"value"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	if err := dc.deviceService.ControlDeviceInHome(userIDInt, homeID, deviceID, req.Command, req.Value); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	updatedDevice, err := dc.deviceService.GetDeviceByIDInHome(userIDInt, homeID, deviceID)
	if err != nil {
		utils.SuccessResponse(c, http.StatusOK, "Điều khiển thiết bị thành công", gin.H{
			"command":   req.Command,
			"value":     req.Value,
			"device_id": deviceID,
		})
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Điều khiển thiết bị thành công", gin.H{
		"command":        req.Command,
		"value":          req.Value,
		"device_id":      deviceID,
		"updated_status": updatedDevice.Status,
		"updated_at":     updatedDevice.UpdatedAt,
	})
}

func (dc *DeviceController) GetRealTimeDeviceStatus(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	device, err := dc.deviceService.GetDeviceByIDInHome(userIDInt, homeID, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy thông tin thiết bị thành công", device)
}

func (dc *DeviceController) GetAllRealTimeDeviceStatus(c *gin.Context) {
	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	statuses, err := dc.deviceService.GetAllRealTimeDeviceStatusInHome(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy trạng thái tất cả thiết bị thành công", statuses)
}

func (dc *DeviceController) BatchControlDevices(c *gin.Context) {
	var req models.BatchControlRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	response, err := dc.deviceService.BatchControlDevicesInHome(userIDInt, homeID, &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Batch control thành công", response)
}

func (dc *DeviceController) UpdateDeviceProperty(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))
	propertyName := c.Param("property")

	var req struct {
		Value interface{} `json:"value" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	err := dc.deviceService.UpdateDevicePropertyInHome(userIDInt, homeID, deviceID, propertyName, req.Value)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Cập nhật thuộc tính thiết bị thành công", gin.H{
		"device_id": deviceID,
		"property":  propertyName,
		"value":     req.Value,
	})
}

func (dc *DeviceController) StartDeviceMonitoring(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	err := dc.deviceService.MonitorDeviceStatusInHome(userIDInt, homeID, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Bắt đầu monitoring thiết bị thành công", gin.H{
		"device_id": deviceID,
		"status":    "monitoring_started",
	})
}

func (dc *DeviceController) GetDevice(c *gin.Context) {
	deviceID, err := strconv.Atoi(c.Param("device_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID thiết bị không hợp lệ")
		return
	}

	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}
	device, err := dc.deviceService.GetDeviceByID(userIDInt, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy thông tin thiết bị thành công", device)
}

func (dc *DeviceController) GetDevices(c *gin.Context) {
	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}
	devices, err := dc.deviceService.GetDevicesByUserID(userIDInt)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách thiết bị thành công", devices)
}

func (dc *DeviceController) UpdateDevice(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))

	var req models.UpdateDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ")
		return
	}

	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}

	err = dc.deviceService.UpdateDevice(userIDInt, deviceID, &req)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Cập nhật thiết bị thành công", nil)
}

func (dc *DeviceController) DeleteDevice(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))

	userID, _ := c.Get("user_id")
	userIDInt, err := getUserIDAsInt(userID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi xác thực người dùng")
		return
	}
	err = dc.deviceService.DeleteDevice(userIDInt, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Xóa thiết bị thành công", nil)
}

func (dc *DeviceController) GetDeviceTypes(c *gin.Context) {
	deviceTypes, err := dc.deviceService.GetAllDeviceTypes()
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách loại thiết bị thành công", deviceTypes)
}

func (dc *DeviceController) GetDevicesByType(c *gin.Context) {
	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	deviceTypeID, err := strconv.Atoi(c.Param("device_type_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID loại thiết bị không hợp lệ")
		return
	}

	devices, err := dc.deviceService.GetDevicesByTypeInHome(userIDInt, homeID, deviceTypeID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách thiết bị theo loại thành công", devices)
}

func (dc *DeviceController) GetDeviceProperties(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	properties, err := dc.deviceService.GetDevicePropertiesInHome(userIDInt, homeID, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy thuộc tính thiết bị thành công", properties)
}

func (dc *DeviceController) GetDeviceCommands(c *gin.Context) {
	deviceID, _ := strconv.Atoi(c.Param("device_id"))

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	commands, err := dc.deviceService.GetDeviceCommandsInHome(userIDInt, homeID, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	var message string
	if len(commands) == 0 {
		message = "Thiết bị chưa có lệnh điều khiển nào"
	} else {
		message = fmt.Sprintf("Lấy %d lệnh điều khiển thiết bị thành công", len(commands))
	}

	utils.SuccessResponse(c, http.StatusOK, message, commands)
}

func (dc *DeviceController) TestMQTT(c *gin.Context) {
	status := dc.deviceService.GetMQTTStatus()

	if !status["connected"].(bool) {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "MQTT service không khả dụng")
		return
	}

	testCommand := map[string]interface{}{
		"device_id": 999,
		"command":   "test",
		"value":     "hello_mqtt",
		"timestamp": time.Now().Unix(),
		"source":    "test_api",
	}

	err := dc.deviceService.TestMQTTPublish("test/topic", testCommand)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "MQTT publish failed: "+err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "MQTT test thành công", map[string]interface{}{
		"mqtt_status":  status,
		"test_message": testCommand,
	})
}

func (dc *DeviceController) DebugDeviceInfo(c *gin.Context) {
	deviceID, err := strconv.Atoi(c.Param("device_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID thiết bị không hợp lệ")
		return
	}

	device, err := dc.deviceService.GetDeviceByID(0, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy thiết bị: "+err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Debug device info", gin.H{
		"device_id":         device.DeviceID,
		"unique_identifier": device.UniqueIdentifier,
		"name":              device.Name,
		"home_id":           device.HomeID,
		"device_type_id":    device.DeviceTypeID,
		"is_online":         device.IsOnline,
	})
}

func (dc *DeviceController) GetDeviceHistory(c *gin.Context) {
	deviceID, err := strconv.Atoi(c.Param("device_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID thiết bị không hợp lệ")
		return
	}

	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 1000 {
		limit = 50
	}

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	history, err := dc.deviceService.GetDeviceHistoryInHome(userIDInt, homeID, deviceID, limit)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy lịch sử thiết bị thành công", history)
}

func (dc *DeviceController) AddDeviceToArea(c *gin.Context) {
	areaID, err := strconv.Atoi(c.Param("area_id"))
	if err != nil {
		fmt.Printf(" Invalid area_id: %v\n", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid area_id"})
		return
	}

	deviceID, err := strconv.Atoi(c.Param("device_id"))
	if err != nil {
		fmt.Printf(" Invalid device_id: %v\n", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid device_id"})
		return
	}

	fmt.Printf(" AddDeviceToArea API called: areaID=%d, deviceID=%d\n", areaID, deviceID)

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		fmt.Printf(" Home access check failed\n")
		return
	}
	fmt.Printf(" Home access check passed: userID=%d, homeID=%d\n", userIDInt, homeID)

	err = dc.deviceService.AddDeviceToAreaInHome(userIDInt, homeID, deviceID, areaID)
	if err != nil {
		fmt.Printf(" Service error: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	fmt.Printf(" Device added to area successfully\n")
	c.JSON(http.StatusOK, gin.H{"message": "Device added to area successfully"})
}

func (dc *DeviceController) RemoveDeviceFromArea(c *gin.Context) {
	deviceID, err := strconv.Atoi(c.Param("device_id"))
	if err != nil {
		fmt.Printf(" Invalid device_id: %v\n", err)
		utils.ErrorResponse(c, http.StatusBadRequest, "ID thiết bị không hợp lệ")
		return
	}

	fmt.Printf(" RemoveDeviceFromArea API called: deviceID=%d\n", deviceID)

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		fmt.Printf(" Home access check failed\n")
		return
	}
	fmt.Printf(" Home access check passed: userID=%d, homeID=%d\n", userIDInt, homeID)

	err = dc.deviceService.RemoveDeviceFromAreaInHome(userIDInt, homeID, deviceID)
	if err != nil {
		fmt.Printf(" Service error: %v\n", err)
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	fmt.Printf(" Device removed from area successfully\n")
	utils.SuccessResponse(c, http.StatusOK, "Xóa thiết bị khỏi khu vực thành công", nil)
}

func (dc *DeviceController) GetDevicesInArea(c *gin.Context) {
	areaID, err := strconv.Atoi(c.Param("area_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID khu vực không hợp lệ")
		return
	}

	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	devices, err := dc.deviceService.GetDevicesInAreaInHome(userIDInt, homeID, areaID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách thiết bị trong khu vực thành công", devices)
}

func (dc *DeviceController) GetDevicesInHome(c *gin.Context) {
	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	devices, err := dc.deviceService.GetDevicesByHomeID(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách thiết bị trong nhà thành công", devices)
}

func (dc *DeviceController) GetDevicesInHomeWithoutArea(c *gin.Context) {
	userIDInt, homeID, hasAccess := dc.checkHomeAccess(c)
	if !hasAccess {
		return
	}

	devices, err := dc.deviceService.GetDevicesInHomeWithoutArea(userIDInt, homeID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy danh sách thiết bị chưa gán khu vực thành công", devices)
}

func (dc *DeviceController) TestFullControlFlow(c *gin.Context) {
	homeIDStr := c.Param("home_id")
	deviceIDStr := c.Param("device_id")

	homeID, err := strconv.Atoi(homeIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Home ID không hợp lệ")
		return
	}

	deviceID, err := strconv.Atoi(deviceIDStr)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Device ID không hợp lệ")
		return
	}

	var req struct {
		Command string `json:"command" binding:"required"`
		Value   string `json:"value"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Dữ liệu không hợp lệ: "+err.Error())
		return
	}

	fmt.Printf(" Real device control flow: Home %d, Device %d -> %s=%s\n", homeID, deviceID, req.Command, req.Value)

	mqttStatus := dc.deviceService.GetMQTTStatus()
	if !mqttStatus["connected"].(bool) {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "MQTT service không khả dụng")
		return
	}

	device, err := dc.deviceService.GetDeviceByID(0, deviceID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy thiết bị: "+err.Error())
		return
	}

	if device.HomeID == nil || *device.HomeID != homeID {
		utils.ErrorResponse(c, http.StatusBadRequest, "Thiết bị không thuộc nhà này")
		return
	}

	mqttTopic := fmt.Sprintf("home/%d/device/%d/command", homeID, deviceID)
	mqttPayload := map[string]interface{}{
		"device_id": deviceID,
		"command":   req.Command,
		"value":     req.Value,
		"timestamp": time.Now().Unix(),
		"source":    "test_api",
		"home_id":   homeID,
	}

	err = dc.deviceService.TestMQTTPublish(mqttTopic, mqttPayload)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "MQTT publish failed: "+err.Error())
		return
	}

	httpResult := map[string]interface{}{
		"success": false,
		"error":   "HTTP test bỏ qua - chỉ sử dụng MQTT",
		"note":    "Device được điều khiển qua MQTT, không cần HTTP",
	}

	utils.SuccessResponse(c, http.StatusOK, "Điều khiển thiết bị thật thành công", gin.H{
		"mqtt_status":  mqttStatus,
		"device_info":  device,
		"mqtt_command": fmt.Sprintf("%s=%s", req.Command, req.Value),
		"mqtt_topic":   fmt.Sprintf("home/%d/device/%d/command", homeID, deviceID),
		"http_test":    httpResult,
		"flow_summary": gin.H{
			"step_1": " Flutter gửi HTTP request đến Golang API",
			"step_2": " Golang API lấy device thật từ database",
			"step_3": " Golang publish command qua MQTT với topic thật",
			"step_4": " ESP32 nhận MQTT command và thực thi",
			"step_5": " ESP32 gửi response/status update",
		},
	})
}

func (dc *DeviceController) sendDirectHTTPToESP32(ipAddress, command, value string) map[string]interface{} {
	client := &http.Client{Timeout: 5 * time.Second}

	payload := map[string]interface{}{
		"command":   command,
		"value":     value,
		"timestamp": time.Now().Unix(),
		"source":    "direct_http_test",
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return map[string]interface{}{
			"success": false,
			"error":   "Failed to marshal payload: " + err.Error(),
		}
	}

	endpoints := []string{
		fmt.Sprintf("http://%s/api/control", ipAddress),
		fmt.Sprintf("http://%s/control", ipAddress),
		fmt.Sprintf("http://%s/command", ipAddress),
	}

	for _, endpoint := range endpoints {
		resp, err := client.Post(endpoint, "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			continue
		}

		return map[string]interface{}{
			"success":  true,
			"endpoint": endpoint,
			"status":   resp.StatusCode,
			"response": string(body),
		}
	}

	return map[string]interface{}{
		"success": false,
		"error":   "No ESP32 endpoint responded",
		"tried":   endpoints,
	}
}

func (dc *DeviceController) GetMQTTHeartbeatStatus(c *gin.Context) {
	status := dc.deviceService.GetMQTTStatus()
	if !status["connected"].(bool) {
		utils.ErrorResponse(c, http.StatusServiceUnavailable, "MQTT service không khả dụng")
		return
	}

	heartbeatStatus := dc.deviceService.GetMQTTHeartbeatStatus()

	utils.SuccessResponse(c, http.StatusOK, "Lấy trạng thái heartbeat thành công", map[string]interface{}{
		"mqtt_status":       status,
		"devices_heartbeat": heartbeatStatus,
	})
}

func (dc *DeviceController) ForceCheckDevicesTimeout(c *gin.Context) {
	dc.deviceService.ForceCheckAllDevicesTimeout()
	utils.SuccessResponse(c, http.StatusOK, "Đã kiểm tra timeout cho tất cả devices", nil)
}
