package controller

import (
	"net/http"
	"strconv"

	"dh52110724-api-quan-ly-nha-thong-minh/service"
	"dh52110724-api-quan-ly-nha-thong-minh/utils"

	"github.com/gin-gonic/gin"
)

type DeviceTypeController struct {
	deviceService *service.DeviceService
}

func NewDeviceTypeController(deviceService *service.DeviceService) *DeviceTypeController {
	return &DeviceTypeController{
		deviceService: deviceService,
	}
}

func (dtc *DeviceTypeController) GetAllDeviceTypes(c *gin.Context) {
	deviceTypes, err := dtc.deviceService.GetAllDeviceTypes()
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách loại thiết bị: "+err.Error())
		return
	}

	utils.SuccessResponse(c, http.Status<PERSON>, "<PERSON><PERSON><PERSON> danh sách loại thiết bị thành công", deviceTypes)
}

func (dtc *DeviceTypeController) GetDeviceType(c *gin.Context) {
	deviceTypeID, err := strconv.Atoi(c.Param("device_type_id"))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "ID loại thiết bị không hợp lệ")
		return
	}

	deviceType, err := dtc.deviceService.GetDeviceTypeByID(deviceTypeID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "Không tìm thấy loại thiết bị: "+err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lấy thông tin loại thiết bị thành công", deviceType)
}
