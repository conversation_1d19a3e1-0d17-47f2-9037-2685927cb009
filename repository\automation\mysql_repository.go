package automation

import (
	"dh52110724-api-quan-ly-nha-thong-minh/models"
	"time"

	"gorm.io/gorm"
)

type mysqlAutomationRepository struct {
	db *gorm.DB
}

func NewPostgresRepository(db *gorm.DB) Repository {
	return &mysqlAutomationRepository{db: db}
}

func (r *mysqlAutomationRepository) CreateRule(rule *models.AutomationRule) (int, error) {
	if err := r.db.Create(rule).Error; err != nil {
		return 0, err
	}
	return rule.RuleID, nil
}

func (r *mysqlAutomationRepository) GetRuleByID(ruleID int) (*models.AutomationRule, error) {
	var rule models.AutomationRule
	err := r.db.Preload("Conditions").Preload("Actions").
		Where("rule_id = ?", ruleID).First(&rule).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

func (r *mysqlAutomationRepository) GetRulesByUserID(userID int) ([]models.AutomationRule, error) {
	var rules []models.AutomationRule
	err := r.db.Preload("Conditions").Preload("Actions").
		Where("user_id = ?", userID).Find(&rules).Error
	return rules, err
}

func (r *mysqlAutomationRepository) GetRulesByHomeID(homeID int) ([]models.AutomationRule, error) {
	var rules []models.AutomationRule
	err := r.db.Debug().Preload("Conditions").
		Preload("Actions").
		Preload("Schedule").
		Joins(`
			JOIN automation_actions aa ON aa.rule_id = automation_rules.rule_id
			JOIN devices d ON d.device_id = aa.device_id
		`).
		Where("d.home_id = ?", homeID).
		Group("automation_rules.rule_id").
		Find(&rules).Error
	return rules, err
}

func (r *mysqlAutomationRepository) UpdateRule(rule *models.AutomationRule) error {
	return r.db.Save(rule).Error
}

func (r *mysqlAutomationRepository) UpdateRuleStatus(ruleID int, isActive bool) error {
	return r.db.Model(&models.AutomationRule{}).
		Where("rule_id = ?", ruleID).
		Update("is_active", isActive).Error
}

func (r *mysqlAutomationRepository) DeleteRule(ruleID int) error {
	return r.db.Where("rule_id = ?", ruleID).Delete(&models.AutomationRule{}).Error
}

func (r *mysqlAutomationRepository) GetActiveConditionRules() ([]models.AutomationRule, error) {
	var rules []models.AutomationRule
	err := r.db.Preload("Conditions").Preload("Actions").
		Where("is_active = ? AND rule_type = ?", true, "condition").
		Find(&rules).Error
	return rules, err
}

func (r *mysqlAutomationRepository) GetActiveScheduleRules() ([]models.AutomationRule, error) {
	var rules []models.AutomationRule
	err := r.db.Preload("Actions").Preload("Schedule").
		Where("is_active = ? AND rule_type = ?", true, "schedule").
		Find(&rules).Error
	return rules, err
}

func (r *mysqlAutomationRepository) CreateSchedule(schedule *models.RuleSchedule) error {
	return r.db.Create(schedule).Error
}

func (r *mysqlAutomationRepository) GetScheduleByRuleID(ruleID int) (*models.RuleSchedule, error) {
	var schedule models.RuleSchedule
	err := r.db.Where("rule_id = ?", ruleID).First(&schedule).Error
	if err != nil {
		return nil, err
	}
	return &schedule, nil
}

func (r *mysqlAutomationRepository) UpdateSchedule(schedule *models.RuleSchedule) error {
	return r.db.Save(schedule).Error
}

func (r *mysqlAutomationRepository) DeleteScheduleByRuleID(ruleID int) error {
	return r.db.Where("rule_id = ?", ruleID).Delete(&models.RuleSchedule{}).Error
}

func (r *mysqlAutomationRepository) GetSchedulesDueForExecution() ([]models.RuleSchedule, error) {
	var schedules []models.RuleSchedule
	now := time.Now()
	err := r.db.Preload("Rule").Preload("Rule.Actions").
		Where("next_run_at <= ? AND rule_id IN (SELECT rule_id FROM automation_rules WHERE is_active = ?)",
			now, true).
		Find(&schedules).Error
	return schedules, err
}

func (r *mysqlAutomationRepository) UpdateScheduleNextRun(ruleID int, nextRun *time.Time) error {
	updates := map[string]interface{}{
		"last_run_at": time.Now(),
	}
	if nextRun != nil {
		updates["next_run_at"] = *nextRun
	}
	return r.db.Model(&models.RuleSchedule{}).
		Where("rule_id = ?", ruleID).
		Updates(updates).Error
}

func (r *mysqlAutomationRepository) GetRuleTemplates() ([]models.RuleTemplate, error) {
	var templates []models.RuleTemplate
	err := r.db.Order("is_system_template DESC, name ASC").Find(&templates).Error
	return templates, err
}

func (r *mysqlAutomationRepository) GetRuleTemplateByID(templateID int) (*models.RuleTemplate, error) {
	var template models.RuleTemplate
	err := r.db.Where("template_id = ?", templateID).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *mysqlAutomationRepository) CreateRuleTemplate(template *models.RuleTemplate) (int, error) {
	if err := r.db.Create(template).Error; err != nil {
		return 0, err
	}
	return template.TemplateID, nil
}

func (r *mysqlAutomationRepository) CreateCondition(condition *models.RuleCondition) error {
	return r.db.Create(condition).Error
}

func (r *mysqlAutomationRepository) GetConditionsByRuleID(ruleID int) ([]models.RuleCondition, error) {
	var conditions []models.RuleCondition
	err := r.db.Where("rule_id = ?", ruleID).Find(&conditions).Error
	return conditions, err
}

func (r *mysqlAutomationRepository) UpdateCondition(condition *models.RuleCondition) error {
	return r.db.Save(condition).Error
}

func (r *mysqlAutomationRepository) DeleteConditionsByRuleID(ruleID int) error {
	return r.db.Where("rule_id = ?", ruleID).Delete(&models.RuleCondition{}).Error
}

func (r *mysqlAutomationRepository) CreateAction(action *models.RuleAction) error {
	return r.db.Create(action).Error
}

func (r *mysqlAutomationRepository) GetActionsByRuleID(ruleID int) ([]models.RuleAction, error) {
	var actions []models.RuleAction
	err := r.db.Where("rule_id = ?", ruleID).Find(&actions).Error
	return actions, err
}

func (r *mysqlAutomationRepository) UpdateAction(action *models.RuleAction) error {
	return r.db.Save(action).Error
}

func (r *mysqlAutomationRepository) DeleteActionsByRuleID(ruleID int) error {
	return r.db.Where("rule_id = ?", ruleID).Delete(&models.RuleAction{}).Error
}

func (r *mysqlAutomationRepository) CreateExecutionLog(log *models.RuleExecutionLog) error {
	return r.db.Create(log).Error
}

func (r *mysqlAutomationRepository) GetExecutionHistory(ruleID int, limit int) ([]models.RuleExecutionLog, error) {
	var logs []models.RuleExecutionLog
	err := r.db.Where("rule_id = ?", ruleID).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error
	return logs, err
}

func (r *mysqlAutomationRepository) GetExecutionHistoryByUserID(userID int, limit int) ([]models.RuleExecutionLog, error) {
	var logs []models.RuleExecutionLog
	err := r.db.Preload("Rule").
		Joins("JOIN automation_rules ar ON ar.rule_id = rule_execution_logs.rule_id").
		Where("ar.user_id = ?", userID).
		Order("rule_execution_logs.created_at DESC").
		Limit(limit).
		Find(&logs).Error
	return logs, err
}

func (r *mysqlAutomationRepository) DeleteOldExecutionLogs(days int) error {
	return r.db.Where("created_at < ?", time.Now().AddDate(0, 0, -days)).
		Delete(&models.RuleExecutionLog{}).Error
}
