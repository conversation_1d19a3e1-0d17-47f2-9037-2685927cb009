package service

import (
	"dh52110724-api-quan-ly-nha-thong-minh/repository/device"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

type MQTTService struct {
	client        mqtt.Client
	broker        string
	clientID      string
	username      string
	password      string
	deviceRepo    device.Repository
	lastHeartbeat map[string]time.Time
	heartbeatMux  sync.RWMutex
}

type MQTTConfig struct {
	Broker   string
	Port     int
	ClientID string
	Username string
	Password string
}

type MQTTCommand struct {
	UniqueIdentifier string `json:"unique_identifier"`
	Command          string `json:"command"`
	Value            string `json:"value"`
	Timestamp        int64  `json:"timestamp"`
	Source           string `json:"source"`
}

func NewMQTTService(config MQTTConfig, deviceRepo device.Repository) *MQTTService {
	var brokerURL string
	if config.Port == 8883 || config.Port == 8884 {
		brokerURL = fmt.Sprintf("tls://%s:%d", config.Broker, config.Port)
	} else {
		brokerURL = fmt.Sprintf("tcp://%s:%d", config.Broker, config.Port)
	}

	service := &MQTTService{
		broker:        brokerURL,
		clientID:      config.ClientID,
		username:      config.Username,
		password:      config.Password,
		deviceRepo:    deviceRepo,
		lastHeartbeat: make(map[string]time.Time),
		heartbeatMux:  sync.RWMutex{},
	}

	service.connect()

	go service.startHeartbeatMonitor()

	return service
}

func (m *MQTTService) connect() {
	opts := mqtt.NewClientOptions()
	opts.AddBroker(m.broker)
	opts.SetClientID(m.clientID)
	opts.SetUsername(m.username)
	opts.SetPassword(m.password)
	opts.SetKeepAlive(60 * time.Second)
	opts.SetDefaultPublishHandler(m.messagePubHandler)
	opts.SetPingTimeout(1 * time.Second)

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		log.Printf("MQTT Connected to broker: %s", m.broker)
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		log.Printf("MQTT Connection lost: %v", err)
	})

	m.client = mqtt.NewClient(opts)
	if token := m.client.Connect(); token.Wait() && token.Error() != nil {
		log.Printf("MQTT Connection failed: %v", token.Error())
	}
}

func (m *MQTTService) messagePubHandler(client mqtt.Client, msg mqtt.Message) {
	log.Printf("MQTT Message received: %s from topic: %s", msg.Payload(), msg.Topic())
}

func (m *MQTTService) PublishCommand(homeID int, deviceID int, command string, value string) error {
	if !m.client.IsConnected() {
		return fmt.Errorf("MQTT client not connected")
	}

	topic := fmt.Sprintf("home/%d/device/%d/command", homeID, deviceID)

	mqttCmd := MQTTCommand{
		UniqueIdentifier: fmt.Sprintf("device_%d", deviceID),
		Command:          command,
		Value:            value,
		Timestamp:        time.Now().Unix(),
		Source:           "backend_api",
	}

	payload, err := json.Marshal(mqttCmd)
	if err != nil {
		return fmt.Errorf("failed to marshal MQTT command: %v", err)
	}

	token := m.client.Publish(topic, 1, false, payload)
	token.Wait()

	if token.Error() != nil {
		return fmt.Errorf("failed to publish MQTT message: %v", token.Error())
	}

	log.Printf("MQTT Command sent to topic %s: %s=%s", topic, command, value)
	return nil
}

func (m *MQTTService) PublishCommandWithUniqueID(homeID int, deviceID int, uniqueIdentifier string, command string, value string) error {
	if !m.client.IsConnected() {
		return fmt.Errorf("MQTT client not connected")
	}

	topic := fmt.Sprintf("home/%d/device/%s/command", homeID, uniqueIdentifier)

	mqttCmd := MQTTCommand{
		UniqueIdentifier: uniqueIdentifier,
		Command:          command,
		Value:            value,
		Timestamp:        time.Now().Unix(),
		Source:           "backend_api",
	}

	payload, err := json.Marshal(mqttCmd)
	if err != nil {
		return fmt.Errorf("failed to marshal MQTT command: %v", err)
	}

	token := m.client.Publish(topic, 1, false, payload)
	token.Wait()

	if token.Error() != nil {
		return fmt.Errorf("failed to publish MQTT message: %v", token.Error())
	}

	log.Printf("MQTT Command sent to topic %s: %s=%s (device: %s)", topic, command, value, uniqueIdentifier)
	return nil
}

func (m *MQTTService) Subscribe(topic string, callback mqtt.MessageHandler) error {
	if !m.client.IsConnected() {
		return fmt.Errorf("MQTT client not connected")
	}

	token := m.client.Subscribe(topic, 1, callback)
	token.Wait()

	if token.Error() != nil {
		return fmt.Errorf("failed to subscribe to topic %s: %v", topic, token.Error())
	}

	log.Printf("MQTT Subscribed to topic: %s", topic)
	return nil
}

func (m *MQTTService) SubscribeDeviceStatus(homeID int) error {
	topic := fmt.Sprintf("home/%d/device/+/status", homeID)

	callback := func(client mqtt.Client, msg mqtt.Message) {
		log.Printf("Device Status Update: %s from %s", msg.Payload(), msg.Topic())

		var status map[string]interface{}
		if err := json.Unmarshal(msg.Payload(), &status); err != nil {
			log.Printf("Error parsing device status: %v", err)
			return
		}

		topicParts := strings.Split(msg.Topic(), "/")
		if len(topicParts) >= 4 {
			if deviceIDStr := topicParts[3]; deviceIDStr != "" {
				if deviceID, err := strconv.Atoi(deviceIDStr); err == nil {
					log.Printf("Device %d status updated: %+v", deviceID, status)
				}
			}
		}
	}

	return m.Subscribe(topic, callback)
}

func (m *MQTTService) IsConnected() bool {
	return m.client != nil && m.client.IsConnected()
}

func (m *MQTTService) Disconnect() {
	if m.client != nil && m.client.IsConnected() {
		m.client.Disconnect(250)
		log.Println("MQTT Disconnected")
	}
}

func (m *MQTTService) SubscribeAllTopics() error {
	if err := m.Subscribe("home/+/device/+/status", m.handleDeviceStatus); err != nil {
		return fmt.Errorf("failed to subscribe device status: %v", err)
	}

	if err := m.Subscribe("home/+/device/+/sensor", m.handleSensorData); err != nil {
		return fmt.Errorf("failed to subscribe sensor data: %v", err)
	}

	if err := m.Subscribe("system/discovery", m.handleDeviceDiscovery); err != nil {
		return fmt.Errorf("failed to subscribe device discovery: %v", err)
	}

	if err := m.Subscribe("system/heartbeat", m.handleHeartbeat); err != nil {
		return fmt.Errorf("failed to subscribe heartbeat: %v", err)
	}

	log.Println("All MQTT topics subscribed successfully")
	return nil
}

func (m *MQTTService) handleDeviceStatus(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Processing device status from topic: %s", msg.Topic())

	var statusData map[string]interface{}
	if err := json.Unmarshal(msg.Payload(), &statusData); err != nil {
		log.Printf("Error parsing device status: %v", err)
		return
	}

	topicParts := strings.Split(msg.Topic(), "/")
	if len(topicParts) >= 4 {
		uniqueIdentifier := topicParts[3]

		currentState, ok := statusData["current_state"].(map[string]interface{})
		if !ok {
			log.Printf("Missing current_state in status payload")
			return
		}

		isOnline, _ := currentState["is_online"].(bool)

		// Update heartbeat
		m.heartbeatMux.Lock()
		m.lastHeartbeat[uniqueIdentifier] = time.Now()
		m.heartbeatMux.Unlock()

		// Handle different device types
		if power, exists := currentState["power"]; exists {
			// Smart Light/Switch - has power state
			powerBool, _ := power.(bool)
			brightness, _ := currentState["brightness"].(float64)

			log.Printf("Device %s (Light/Switch) status: power=%t, brightness=%.0f, online=%t",
				uniqueIdentifier, powerBool, brightness, isOnline)

			if err := m.updateDeviceStatusInDB(uniqueIdentifier, powerBool, int(brightness), isOnline); err != nil {
				log.Printf("Failed to update device status in DB: %v", err)
			} else {
				log.Printf("Status updated in database for device: %s (power=%t, online=%t)", uniqueIdentifier, powerBool, isOnline)
			}
		} else if lightLevel, exists := currentState["light_level"]; exists {
			// Sensor - has sensor values
			lightLevelFloat, _ := lightLevel.(float64)
			rawValue, _ := currentState["raw_value"].(float64)

			log.Printf("Device %s (Sensor) status: light_level=%.0f, raw_value=%.0f, online=%t",
				uniqueIdentifier, lightLevelFloat, rawValue, isOnline)

			if err := m.updateSensorStatusInDB(uniqueIdentifier, lightLevelFloat, rawValue, isOnline); err != nil {
				log.Printf("Failed to update sensor status in DB: %v", err)
			} else {
				log.Printf("Sensor status updated in database for device: %s (light_level=%.0f, online=%t)", uniqueIdentifier, lightLevelFloat, isOnline)
			}
		} else {
			// Generic device - only update online status
			log.Printf("Device %s (Generic) status: online=%t", uniqueIdentifier, isOnline)

			if err := m.updateDeviceOnlineStatus(uniqueIdentifier, isOnline); err != nil {
				log.Printf("Failed to update device online status in DB: %v", err)
			} else {
				log.Printf("Online status updated in database for device: %s (online=%t)", uniqueIdentifier, isOnline)
			}
		}
	}
}

func (m *MQTTService) handleSensorData(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Sensor Data: %s from %s", msg.Payload(), msg.Topic())
}

func (m *MQTTService) handleDeviceDiscovery(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Device Discovery: %s from %s", msg.Payload(), msg.Topic())
}

func (m *MQTTService) handleHeartbeat(client mqtt.Client, msg mqtt.Message) {
	log.Printf("Heartbeat: %s from %s", msg.Payload(), msg.Topic())

	var heartbeatData map[string]interface{}
	if err := json.Unmarshal(msg.Payload(), &heartbeatData); err != nil {
		log.Printf("Error parsing heartbeat: %v", err)
		return
	}

	uniqueIdentifier, ok := heartbeatData["unique_identifier"].(string)
	if !ok {
		log.Printf("Invalid heartbeat: missing unique_identifier")
		return
	}

	m.heartbeatMux.Lock()
	m.lastHeartbeat[uniqueIdentifier] = time.Now()
	m.heartbeatMux.Unlock()

	if err := m.updateDeviceOnlineByUniqueID(uniqueIdentifier, true); err != nil {
		log.Printf("Failed to update device online status: %v", err)
	}

	log.Printf("Heartbeat received from device: %s", uniqueIdentifier)
}

func (m *MQTTService) updateDeviceOnlineByUniqueID(uniqueIdentifier string, isOnline bool) error {
	device, err := m.deviceRepo.GetDeviceByUniqueIdentifier(uniqueIdentifier)
	if err != nil {
		return fmt.Errorf("device not found with unique_identifier %s: %v", uniqueIdentifier, err)
	}

	return m.deviceRepo.UpdateDeviceOnlineStatus(device.DeviceID, isOnline)
}

func (m *MQTTService) startHeartbeatMonitor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	log.Printf("Heartbeat monitor started - checking every 30 seconds")

	for range ticker.C {
		m.checkHeartbeatTimeouts()
	}
}

func (m *MQTTService) checkHeartbeatTimeouts() {
	now := time.Now()
	timeoutDuration := 2 * time.Minute

	m.heartbeatMux.RLock()
	defer m.heartbeatMux.RUnlock()

	for uniqueIdentifier, lastHeartbeat := range m.lastHeartbeat {
		if now.Sub(lastHeartbeat) > timeoutDuration {
			log.Printf("Device %s timeout detected, marking as offline", uniqueIdentifier)

			if err := m.updateDeviceOnlineByUniqueID(uniqueIdentifier, false); err != nil {
				log.Printf("Failed to mark device %s as offline: %v", uniqueIdentifier, err)
			} else {
				log.Printf("Device %s marked as offline due to heartbeat timeout", uniqueIdentifier)
			}

			m.heartbeatMux.RUnlock()
			m.heartbeatMux.Lock()
			delete(m.lastHeartbeat, uniqueIdentifier)
			m.heartbeatMux.Unlock()
			m.heartbeatMux.RLock()
		}
	}
}

func (m *MQTTService) updateDeviceStatusInDB(uniqueIdentifier string, power bool, brightness int, isOnline bool) error {
	device, err := m.deviceRepo.GetDeviceByUniqueIdentifier(uniqueIdentifier)
	if err != nil {
		return fmt.Errorf("device not found with unique_identifier %s: %v", uniqueIdentifier, err)
	}

	if err := m.deviceRepo.UpdateDeviceOnlineStatus(device.DeviceID, isOnline); err != nil {
		log.Printf("Failed to update online status for device %d: %v", device.DeviceID, err)
	}

	statusValue := "off"
	if power {
		statusValue = "on"
	}

	if err := m.deviceRepo.UpdateDeviceStatus(device.DeviceID, statusValue); err != nil {
		log.Printf("Failed to update device status for device %d: %v", device.DeviceID, err)
	}

	log.Printf(" Successfully updated device %s (ID: %d) in database", uniqueIdentifier, device.DeviceID)
	return nil
}

func (m *MQTTService) updateSensorStatusInDB(uniqueIdentifier string, lightLevel float64, rawValue float64, isOnline bool) error {
	device, err := m.deviceRepo.GetDeviceByUniqueIdentifier(uniqueIdentifier)
	if err != nil {
		return fmt.Errorf("device not found with unique_identifier %s: %v", uniqueIdentifier, err)
	}

	// Update online status
	if err := m.deviceRepo.UpdateDeviceOnlineStatus(device.DeviceID, isOnline); err != nil {
		log.Printf("Failed to update online status for sensor %d: %v", device.DeviceID, err)
	}

	// For sensors, store the light_level as status value
	statusValue := fmt.Sprintf("%.0f", lightLevel)
	if err := m.deviceRepo.UpdateDeviceStatus(device.DeviceID, statusValue); err != nil {
		log.Printf("Failed to update sensor status for device %d: %v", device.DeviceID, err)
	}

	log.Printf("Successfully updated sensor %s (ID: %d) with light_level=%.0f in database", uniqueIdentifier, device.DeviceID, lightLevel)
	return nil
}

func (m *MQTTService) updateDeviceOnlineStatus(uniqueIdentifier string, isOnline bool) error {
	device, err := m.deviceRepo.GetDeviceByUniqueIdentifier(uniqueIdentifier)
	if err != nil {
		return fmt.Errorf("device not found with unique_identifier %s: %v", uniqueIdentifier, err)
	}

	if err := m.deviceRepo.UpdateDeviceOnlineStatus(device.DeviceID, isOnline); err != nil {
		return fmt.Errorf("failed to update online status for device %d: %v", device.DeviceID, err)
	}

	log.Printf("Successfully updated online status for device %s (ID: %d) to %t", uniqueIdentifier, device.DeviceID, isOnline)
	return nil
}

func (m *MQTTService) GetConnectionStatus() map[string]interface{} {
	return map[string]interface{}{
		"connected": m.IsConnected(),
		"broker":    m.broker,
		"client_id": m.clientID,
	}
}

func (m *MQTTService) ForceCheckAllDevicesTimeout() {
	log.Printf("Force checking all devices for timeout...")
	m.checkHeartbeatTimeouts()
}

func (m *MQTTService) GetHeartbeatStatus() map[string]interface{} {
	m.heartbeatMux.RLock()
	defer m.heartbeatMux.RUnlock()

	status := make(map[string]interface{})
	now := time.Now()

	for uniqueID, lastHeartbeat := range m.lastHeartbeat {
		timeSinceLastHeartbeat := now.Sub(lastHeartbeat)
		status[uniqueID] = map[string]interface{}{
			"last_heartbeat": lastHeartbeat.Format("2006-01-02 15:04:05"),
			"seconds_ago":    int(timeSinceLastHeartbeat.Seconds()),
			"is_timeout":     timeSinceLastHeartbeat > 2*time.Minute,
		}
	}

	return status
}
