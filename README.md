# Smart Home Management API

> API quản lý nhà thông minh với điều khiển thiết bị IoT, tự động hóa và giám sát real-time.

## Tính năng

- **Authentication**: Đ<PERSON><PERSON> ký/đăng nhập với <PERSON>, ph<PERSON> quyền OWNER/ADMIN/MEMBER
- **Home Management**: Quản lý nhà, khu vực, thành viên
- **Device Control**: Đi<PERSON>u khiển thiết bị ESP32 qua HTTP/MQTT/Bluetooth
- **Automation**: <PERSON><PERSON><PERSON> đ<PERSON><PERSON> k<PERSON>, kịch bản IF-THEN dựa trên sensor
- **Monitoring**: Cảnh báo real-time, logs, thống kê

## Setup

**Requirements:** Go 1.21+, MySQL 8.0+

```bash
git clone <repo>
cd smart-home-api
cp .env.example .env
go mod download
mysql -u root -p -e "CREATE DATABASE smart_home_db;"
go run migrations/migrate.go
```

5. **Start server**
```bash
go run main.go
```

Server sẽ chạy tại: `http://localhost:8080`

### Docker Setup (Alternative)
```bash
# Build and run with Docker Compose
docker-compose up -d

# Check logs
docker-compose logs -f api
```

## Kiến trúc hệ thống
```
[Flutter Mobile App] ←→ [Golang Backend API] ←→ [MySQL Database]
                                ↓
                         [MQTT Broker (HiveMQ)]
                                ↓
                         [ESP32 IoT Devices]
```

## Thành phần Backend

**config/** - Quản lý cấu hình ứng dụng
- `config.go` - Load cấu hình từ file .env

**controller/** - Xử lý HTTP Requests
- `auth_controller.go` - APIs xác thực
- `home_controller.go` - APIs quản lý nhà
- `device_controller.go` - APIs điều khiển thiết bị
- `automation_controller.go` - APIs tự động hóa
- `monitoring_controller.go` - APIs giám sát

**service/** - Business logic
- `auth_service.go` - Logic authentication
- `device_service.go` - Logic điều khiển thiết bị
- `automation_service.go` - Logic tự động hóa
- `monitoring_service.go` - Logic giám sát
- `notification_service.go` - Logic thông báo

**repository/** - Tầng truy cập dữ liệu
- `auth/` - Repository cho authentication
- `device/` - Repository cho device management
- `automation/` - Repository cho automation

**models/** - Định nghĩa cấu trúc dữ liệu
- `auth.go` - User, authentication models
- `device.go` - Device, area models
- `automation.go` - Automation rule models

